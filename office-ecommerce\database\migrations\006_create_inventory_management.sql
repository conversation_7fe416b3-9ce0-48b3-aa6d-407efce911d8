USE OfficeEcommerce;
GO

-- Create ProductAttributes table for customization options
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ProductAttributes' AND xtype='U')
BEGIN
    CREATE TABLE ProductAttributes (
        id INT PRIMARY KEY IDENTITY(1,1),
        name NVARCHAR(100) NOT NULL,
        type NVARCHAR(50) NOT NULL, -- 'color', 'material', 'dimension', 'finish', 'accessory'
        value NVARCHAR(255) NOT NULL,
        displayName NVARCHAR(255) NOT NULL,
        hexColor NVARCHAR(7) NULL, -- For color attributes
        priceModifier DECIMAL(10,2) DEFAULT 0, -- Additional cost/discount
        isActive BIT DEFAULT 1,
        sortOrder INT DEFAULT 0,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE()
    );

    -- Create indexes
    CREATE INDEX IX_ProductAttributes_Type ON ProductAttributes(type);
    CREATE INDEX IX_ProductAttributes_IsActive ON ProductAttributes(isActive);
    CREATE INDEX IX_ProductAttributes_SortOrder ON ProductAttributes(sortOrder);

    PRINT 'ProductAttributes table created successfully';
END
ELSE
BEGIN
    PRINT 'ProductAttributes table already exists';
END
GO

-- Create ProductVariants table for different product configurations
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ProductVariants' AND xtype='U')
BEGIN
    CREATE TABLE ProductVariants (
        id INT PRIMARY KEY IDENTITY(1,1),
        productId INT NOT NULL,
        sku NVARCHAR(100) NOT NULL UNIQUE,
        variantName NVARCHAR(255) NOT NULL,
        attributes NVARCHAR(MAX) NOT NULL, -- JSON of attribute IDs and values
        priceModifier DECIMAL(10,2) DEFAULT 0,
        weight DECIMAL(8,2) NULL,
        dimensions NVARCHAR(MAX) NULL, -- JSON: {"width": 120, "height": 75, "depth": 60}
        isActive BIT DEFAULT 1,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_ProductVariants_Products FOREIGN KEY (productId) REFERENCES Products(id) ON DELETE CASCADE
    );

    -- Create indexes
    CREATE INDEX IX_ProductVariants_ProductId ON ProductVariants(productId);
    CREATE INDEX IX_ProductVariants_SKU ON ProductVariants(sku);
    CREATE INDEX IX_ProductVariants_IsActive ON ProductVariants(isActive);

    PRINT 'ProductVariants table created successfully';
END
ELSE
BEGIN
    PRINT 'ProductVariants table already exists';
END
GO

-- Create Suppliers table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
BEGIN
    CREATE TABLE Suppliers (
        id INT PRIMARY KEY IDENTITY(1,1),
        name NVARCHAR(255) NOT NULL,
        contactPerson NVARCHAR(255) NULL,
        email NVARCHAR(255) NULL,
        phone NVARCHAR(50) NULL,
        address NVARCHAR(500) NULL,
        website NVARCHAR(255) NULL,
        taxId NVARCHAR(50) NULL,
        paymentTerms NVARCHAR(255) NULL,
        isActive BIT DEFAULT 1,
        rating DECIMAL(3,2) NULL, -- 0.00 to 5.00
        notes NVARCHAR(MAX) NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE()
    );

    -- Create indexes
    CREATE INDEX IX_Suppliers_Name ON Suppliers(name);
    CREATE INDEX IX_Suppliers_IsActive ON Suppliers(isActive);
    CREATE INDEX IX_Suppliers_Email ON Suppliers(email);

    PRINT 'Suppliers table created successfully';
END
ELSE
BEGIN
    PRINT 'Suppliers table already exists';
END
GO

-- Create Inventory table for stock tracking
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Inventory' AND xtype='U')
BEGIN
    CREATE TABLE Inventory (
        id INT PRIMARY KEY IDENTITY(1,1),
        productId INT NULL, -- For base products
        variantId INT NULL, -- For product variants
        supplierId INT NULL,
        sku NVARCHAR(100) NOT NULL,
        quantityOnHand INT NOT NULL DEFAULT 0,
        quantityReserved INT NOT NULL DEFAULT 0, -- Reserved for pending orders
        quantityAvailable AS (quantityOnHand - quantityReserved) PERSISTED,
        reorderLevel INT NOT NULL DEFAULT 10,
        maxStockLevel INT NULL,
        unitCost DECIMAL(10,2) NULL,
        lastRestockDate DATETIME NULL,
        location NVARCHAR(255) NULL, -- Warehouse location
        batchNumber NVARCHAR(100) NULL,
        expiryDate DATETIME NULL,
        isActive BIT DEFAULT 1,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_Inventory_Products FOREIGN KEY (productId) REFERENCES Products(id),
        CONSTRAINT FK_Inventory_ProductVariants FOREIGN KEY (variantId) REFERENCES ProductVariants(id),
        CONSTRAINT FK_Inventory_Suppliers FOREIGN KEY (supplierId) REFERENCES Suppliers(id),
        CONSTRAINT CK_Inventory_ProductOrVariant CHECK ((productId IS NOT NULL AND variantId IS NULL) OR (productId IS NULL AND variantId IS NOT NULL)),
        CONSTRAINT CK_Inventory_Quantities CHECK (quantityOnHand >= 0 AND quantityReserved >= 0 AND quantityReserved <= quantityOnHand)
    );

    -- Create indexes
    CREATE INDEX IX_Inventory_ProductId ON Inventory(productId);
    CREATE INDEX IX_Inventory_VariantId ON Inventory(variantId);
    CREATE INDEX IX_Inventory_SKU ON Inventory(sku);
    CREATE INDEX IX_Inventory_QuantityAvailable ON Inventory(quantityAvailable);
    CREATE INDEX IX_Inventory_ReorderLevel ON Inventory(reorderLevel);
    CREATE INDEX IX_Inventory_IsActive ON Inventory(isActive);

    PRINT 'Inventory table created successfully';
END
ELSE
BEGIN
    PRINT 'Inventory table already exists';
END
GO

-- Create StockMovements table for audit trail
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='StockMovements' AND xtype='U')
BEGIN
    CREATE TABLE StockMovements (
        id INT PRIMARY KEY IDENTITY(1,1),
        inventoryId INT NOT NULL,
        movementType NVARCHAR(50) NOT NULL, -- 'IN', 'OUT', 'ADJUSTMENT', 'TRANSFER', 'RESERVED', 'UNRESERVED'
        quantity INT NOT NULL,
        previousQuantity INT NOT NULL,
        newQuantity INT NOT NULL,
        unitCost DECIMAL(10,2) NULL,
        totalCost AS (quantity * unitCost) PERSISTED,
        reason NVARCHAR(255) NULL,
        referenceType NVARCHAR(50) NULL, -- 'ORDER', 'PURCHASE', 'ADJUSTMENT', 'RETURN'
        referenceId INT NULL, -- ID of the related order, purchase, etc.
        performedBy INT NOT NULL,
        notes NVARCHAR(MAX) NULL,
        createdAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_StockMovements_Inventory FOREIGN KEY (inventoryId) REFERENCES Inventory(id) ON DELETE CASCADE,
        CONSTRAINT FK_StockMovements_PerformedBy FOREIGN KEY (performedBy) REFERENCES Users(id),
        CONSTRAINT CK_StockMovements_Type CHECK (movementType IN ('IN', 'OUT', 'ADJUSTMENT', 'TRANSFER', 'RESERVED', 'UNRESERVED'))
    );

    -- Create indexes
    CREATE INDEX IX_StockMovements_InventoryId ON StockMovements(inventoryId);
    CREATE INDEX IX_StockMovements_MovementType ON StockMovements(movementType);
    CREATE INDEX IX_StockMovements_CreatedAt ON StockMovements(createdAt);
    CREATE INDEX IX_StockMovements_PerformedBy ON StockMovements(performedBy);
    CREATE INDEX IX_StockMovements_Reference ON StockMovements(referenceType, referenceId);

    PRINT 'StockMovements table created successfully';
END
ELSE
BEGIN
    PRINT 'StockMovements table already exists';
END
GO

-- Create PurchaseOrders table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PurchaseOrders' AND xtype='U')
BEGIN
    CREATE TABLE PurchaseOrders (
        id INT PRIMARY KEY IDENTITY(1,1),
        poNumber NVARCHAR(100) NOT NULL UNIQUE,
        supplierId INT NOT NULL,
        status NVARCHAR(50) DEFAULT 'DRAFT', -- 'DRAFT', 'SENT', 'CONFIRMED', 'PARTIALLY_RECEIVED', 'RECEIVED', 'CANCELLED'
        orderDate DATETIME DEFAULT GETDATE(),
        expectedDeliveryDate DATETIME NULL,
        actualDeliveryDate DATETIME NULL,
        subtotal DECIMAL(12,2) NOT NULL DEFAULT 0,
        taxAmount DECIMAL(12,2) NOT NULL DEFAULT 0,
        shippingCost DECIMAL(12,2) NOT NULL DEFAULT 0,
        totalAmount DECIMAL(12,2) NOT NULL DEFAULT 0,
        notes NVARCHAR(MAX) NULL,
        createdBy INT NOT NULL,
        approvedBy INT NULL,
        approvedAt DATETIME NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_PurchaseOrders_Suppliers FOREIGN KEY (supplierId) REFERENCES Suppliers(id),
        CONSTRAINT FK_PurchaseOrders_CreatedBy FOREIGN KEY (createdBy) REFERENCES Users(id),
        CONSTRAINT FK_PurchaseOrders_ApprovedBy FOREIGN KEY (approvedBy) REFERENCES Users(id),
        CONSTRAINT CK_PurchaseOrders_Status CHECK (status IN ('DRAFT', 'SENT', 'CONFIRMED', 'PARTIALLY_RECEIVED', 'RECEIVED', 'CANCELLED')),
        CONSTRAINT CK_PurchaseOrders_Amounts CHECK (subtotal >= 0 AND taxAmount >= 0 AND shippingCost >= 0 AND totalAmount >= 0)
    );

    -- Create indexes
    CREATE INDEX IX_PurchaseOrders_PONumber ON PurchaseOrders(poNumber);
    CREATE INDEX IX_PurchaseOrders_SupplierId ON PurchaseOrders(supplierId);
    CREATE INDEX IX_PurchaseOrders_Status ON PurchaseOrders(status);
    CREATE INDEX IX_PurchaseOrders_OrderDate ON PurchaseOrders(orderDate);
    CREATE INDEX IX_PurchaseOrders_CreatedBy ON PurchaseOrders(createdBy);

    PRINT 'PurchaseOrders table created successfully';
END
ELSE
BEGIN
    PRINT 'PurchaseOrders table already exists';
END
GO
