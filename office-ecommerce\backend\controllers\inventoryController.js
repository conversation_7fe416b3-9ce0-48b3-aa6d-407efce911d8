const { getPool, sql } = require('../config/database');

/**
 * Get all inventory items with filtering and pagination
 */
const getAllInventory = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            search,
            lowStock,
            outOfStock,
            category,
            supplier,
            sortBy = 'updatedAt',
            sortOrder = 'DESC'
        } = req.query;

        const offset = (page - 1) * limit;
        const pool = getPool();

        let whereClause = 'WHERE i.isActive = 1';
        const params = [];

        if (search) {
            whereClause += ' AND (p.name LIKE @search OR i.sku LIKE @search OR pv.variantName LIKE @search)';
            params.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
        }

        if (lowStock === 'true') {
            whereClause += ' AND i.quantityAvailable <= i.reorderLevel';
        }

        if (outOfStock === 'true') {
            whereClause += ' AND i.quantityAvailable = 0';
        }

        if (category) {
            whereClause += ' AND c.name = @category';
            params.push({ name: 'category', type: sql.NVarChar, value: category });
        }

        if (supplier) {
            whereClause += ' AND s.name = @supplier';
            params.push({ name: 'supplier', type: sql.NVarChar, value: supplier });
        }

        // Validate sort fields
        const allowedSortFields = ['sku', 'quantityOnHand', 'quantityAvailable', 'reorderLevel', 'unitCost', 'updatedAt'];
        const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'updatedAt';
        const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

        let query = `
            SELECT
                i.id, i.sku, i.quantityOnHand, i.quantityReserved, i.quantityAvailable,
                i.reorderLevel, i.maxStockLevel, i.unitCost, i.lastRestockDate,
                i.location, i.batchNumber, i.expiryDate, i.createdAt, i.updatedAt,
                p.id as productId, p.name as productName, p.price as productPrice,
                pv.id as variantId, pv.variantName, pv.priceModifier,
                c.name as categoryName,
                s.id as supplierId, s.name as supplierName,
                CASE
                    WHEN i.quantityAvailable = 0 THEN 'Out of Stock'
                    WHEN i.quantityAvailable <= i.reorderLevel THEN 'Low Stock'
                    ELSE 'In Stock'
                END as stockStatus
            FROM Inventory i
            LEFT JOIN Products p ON i.productId = p.id
            LEFT JOIN ProductVariants pv ON i.variantId = pv.id
            LEFT JOIN Categories c ON p.categoryId = c.id
            LEFT JOIN Suppliers s ON i.supplierId = s.id
            ${whereClause}
            ORDER BY i.${sortField} ${order}
            OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
        `;

        let countQuery = `
            SELECT COUNT(*) as total
            FROM Inventory i
            LEFT JOIN Products p ON i.productId = p.id
            LEFT JOIN ProductVariants pv ON i.variantId = pv.id
            LEFT JOIN Categories c ON p.categoryId = c.id
            LEFT JOIN Suppliers s ON i.supplierId = s.id
            ${whereClause}
        `;

        const request = pool.request()
            .input('offset', sql.Int, offset)
            .input('limit', sql.Int, parseInt(limit));

        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [inventoryResult, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        const total = countResult.recordset[0].total;
        const totalPages = Math.ceil(total / limit);

        res.json({
            inventory: inventoryResult.recordset,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalItems: total,
                itemsPerPage: parseInt(limit),
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1
            }
        });
    } catch (error) {
        console.error('Get inventory error:', error);
        res.status(500).json({
            message: 'Failed to retrieve inventory',
            code: 'INVENTORY_FETCH_ERROR'
        });
    }
};

/**
 * Get inventory item by ID
 */
const getInventoryById = async (req, res) => {
    try {
        const { id } = req.params;
        const pool = getPool();

        const result = await pool.request()
            .input('id', sql.Int, id)
            .query(`
                SELECT
                    i.*,
                    p.name as productName, p.price as productPrice,
                    pv.variantName, pv.priceModifier,
                    c.name as categoryName,
                    s.name as supplierName, s.contactPerson, s.email as supplierEmail
                FROM Inventory i
                LEFT JOIN Products p ON i.productId = p.id
                LEFT JOIN ProductVariants pv ON i.variantId = pv.id
                LEFT JOIN Categories c ON p.categoryId = c.id
                LEFT JOIN Suppliers s ON i.supplierId = s.id
                WHERE i.id = @id AND i.isActive = 1
            `);

        if (result.recordset.length === 0) {
            return res.status(404).json({
                message: 'Inventory item not found',
                code: 'INVENTORY_NOT_FOUND'
            });
        }

        res.json(result.recordset[0]);
    } catch (error) {
        console.error('Get inventory by ID error:', error);
        res.status(500).json({
            message: 'Failed to retrieve inventory item',
            code: 'INVENTORY_FETCH_ERROR'
        });
    }
};

/**
 * Create new inventory item
 */
const createInventory = async (req, res) => {
    try {
        const {
            productId,
            variantId,
            supplierId,
            sku,
            quantityOnHand,
            reorderLevel,
            maxStockLevel,
            unitCost,
            location,
            batchNumber,
            expiryDate
        } = req.body;

        // Validate that either productId or variantId is provided, but not both
        if ((!productId && !variantId) || (productId && variantId)) {
            return res.status(400).json({
                message: 'Either productId or variantId must be provided, but not both',
                code: 'INVALID_PRODUCT_REFERENCE'
            });
        }

        const pool = getPool();

        // Check if SKU already exists
        const existingResult = await pool.request()
            .input('sku', sql.NVarChar, sku)
            .query('SELECT id FROM Inventory WHERE sku = @sku');

        if (existingResult.recordset.length > 0) {
            return res.status(400).json({
                message: 'SKU already exists',
                code: 'SKU_EXISTS'
            });
        }

        const result = await pool.request()
            .input('productId', sql.Int, productId || null)
            .input('variantId', sql.Int, variantId || null)
            .input('supplierId', sql.Int, supplierId || null)
            .input('sku', sql.NVarChar, sku)
            .input('quantityOnHand', sql.Int, quantityOnHand || 0)
            .input('reorderLevel', sql.Int, reorderLevel || 10)
            .input('maxStockLevel', sql.Int, maxStockLevel || null)
            .input('unitCost', sql.Decimal(10, 2), unitCost || null)
            .input('location', sql.NVarChar, location || null)
            .input('batchNumber', sql.NVarChar, batchNumber || null)
            .input('expiryDate', sql.DateTime, expiryDate || null)
            .query(`
                INSERT INTO Inventory (
                    productId, variantId, supplierId, sku, quantityOnHand,
                    reorderLevel, maxStockLevel, unitCost, location,
                    batchNumber, expiryDate, lastRestockDate
                )
                OUTPUT INSERTED.*
                VALUES (
                    @productId, @variantId, @supplierId, @sku, @quantityOnHand,
                    @reorderLevel, @maxStockLevel, @unitCost, @location,
                    @batchNumber, @expiryDate, GETDATE()
                )
            `);

        const newInventory = result.recordset[0];

        // Log stock movement
        await pool.request()
            .input('inventoryId', sql.Int, newInventory.id)
            .input('quantity', sql.Int, quantityOnHand || 0)
            .input('previousQuantity', sql.Int, 0)
            .input('newQuantity', sql.Int, quantityOnHand || 0)
            .input('unitCost', sql.Decimal(10, 2), unitCost || null)
            .input('performedBy', sql.Int, req.user.id)
            .input('reason', sql.NVarChar, 'Initial stock creation')
            .query(`
                INSERT INTO StockMovements (
                    inventoryId, movementType, quantity, previousQuantity,
                    newQuantity, unitCost, reason, performedBy
                )
                VALUES (
                    @inventoryId, 'IN', @quantity, @previousQuantity,
                    @newQuantity, @unitCost, @reason, @performedBy
                )
            `);

        res.status(201).json({
            message: 'Inventory item created successfully',
            inventory: newInventory
        });
    } catch (error) {
        console.error('Create inventory error:', error);
        res.status(500).json({
            message: 'Failed to create inventory item',
            code: 'INVENTORY_CREATE_ERROR'
        });
    }
};

/**
 * Update inventory item
 */
const updateInventory = async (req, res) => {
    try {
        const { id } = req.params;
        const {
            quantityOnHand,
            reorderLevel,
            maxStockLevel,
            unitCost,
            location,
            batchNumber,
            expiryDate,
            reason
        } = req.body;

        const pool = getPool();

        // Get current inventory data
        const currentResult = await pool.request()
            .input('id', sql.Int, id)
            .query('SELECT * FROM Inventory WHERE id = @id AND isActive = 1');

        if (currentResult.recordset.length === 0) {
            return res.status(404).json({
                message: 'Inventory item not found',
                code: 'INVENTORY_NOT_FOUND'
            });
        }

        const currentInventory = currentResult.recordset[0];
        const previousQuantity = currentInventory.quantityOnHand;

        const result = await pool.request()
            .input('id', sql.Int, id)
            .input('quantityOnHand', sql.Int, quantityOnHand ?? currentInventory.quantityOnHand)
            .input('reorderLevel', sql.Int, reorderLevel ?? currentInventory.reorderLevel)
            .input('maxStockLevel', sql.Int, maxStockLevel ?? currentInventory.maxStockLevel)
            .input('unitCost', sql.Decimal(10, 2), unitCost ?? currentInventory.unitCost)
            .input('location', sql.NVarChar, location ?? currentInventory.location)
            .input('batchNumber', sql.NVarChar, batchNumber ?? currentInventory.batchNumber)
            .input('expiryDate', sql.DateTime, expiryDate ?? currentInventory.expiryDate)
            .query(`
                UPDATE Inventory
                SET quantityOnHand = @quantityOnHand,
                    reorderLevel = @reorderLevel,
                    maxStockLevel = @maxStockLevel,
                    unitCost = @unitCost,
                    location = @location,
                    batchNumber = @batchNumber,
                    expiryDate = @expiryDate,
                    updatedAt = GETDATE()
                OUTPUT INSERTED.*
                WHERE id = @id
            `);

        const updatedInventory = result.recordset[0];

        // Log stock movement if quantity changed
        if (quantityOnHand !== undefined && quantityOnHand !== previousQuantity) {
            const quantityDiff = quantityOnHand - previousQuantity;
            const movementType = quantityDiff > 0 ? 'IN' : 'OUT';

            await pool.request()
                .input('inventoryId', sql.Int, id)
                .input('movementType', sql.NVarChar, movementType)
                .input('quantity', sql.Int, Math.abs(quantityDiff))
                .input('previousQuantity', sql.Int, previousQuantity)
                .input('newQuantity', sql.Int, quantityOnHand)
                .input('unitCost', sql.Decimal(10, 2), unitCost || currentInventory.unitCost)
                .input('reason', sql.NVarChar, reason || 'Manual adjustment')
                .input('performedBy', sql.Int, req.user.id)
                .query(`
                    INSERT INTO StockMovements (
                        inventoryId, movementType, quantity, previousQuantity,
                        newQuantity, unitCost, reason, performedBy
                    )
                    VALUES (
                        @inventoryId, @movementType, @quantity, @previousQuantity,
                        @newQuantity, @unitCost, @reason, @performedBy
                    )
                `);
        }

        res.json({
            message: 'Inventory item updated successfully',
            inventory: updatedInventory
        });
    } catch (error) {
        console.error('Update inventory error:', error);
        res.status(500).json({
            message: 'Failed to update inventory item',
            code: 'INVENTORY_UPDATE_ERROR'
        });
    }
};

/**
 * Get stock movements for an inventory item
 */
const getStockMovements = async (req, res) => {
    try {
        const { id } = req.params;
        const { page = 1, limit = 20 } = req.query;
        const offset = (page - 1) * limit;

        const pool = getPool();

        const result = await pool.request()
            .input('inventoryId', sql.Int, id)
            .input('offset', sql.Int, offset)
            .input('limit', sql.Int, parseInt(limit))
            .query(`
                SELECT
                    sm.*,
                    u.firstName + ' ' + u.lastName as performedByName
                FROM StockMovements sm
                LEFT JOIN Users u ON sm.performedBy = u.id
                WHERE sm.inventoryId = @inventoryId
                ORDER BY sm.createdAt DESC
                OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
            `);

        const countResult = await pool.request()
            .input('inventoryId', sql.Int, id)
            .query('SELECT COUNT(*) as total FROM StockMovements WHERE inventoryId = @inventoryId');

        const total = countResult.recordset[0].total;
        const totalPages = Math.ceil(total / limit);

        res.json({
            movements: result.recordset,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalItems: total,
                itemsPerPage: parseInt(limit)
            }
        });
    } catch (error) {
        console.error('Get stock movements error:', error);
        res.status(500).json({
            message: 'Failed to retrieve stock movements',
            code: 'STOCK_MOVEMENTS_FETCH_ERROR'
        });
    }
};

/**
 * Get inventory dashboard data
 */
const getInventoryDashboard = async (req, res) => {
    try {
        const pool = getPool();

        const result = await pool.request().query(`
            SELECT
                COUNT(*) as totalItems,
                SUM(quantityOnHand) as totalStock,
                SUM(CASE WHEN quantityAvailable <= reorderLevel THEN 1 ELSE 0 END) as lowStockItems,
                SUM(CASE WHEN quantityAvailable = 0 THEN 1 ELSE 0 END) as outOfStockItems,
                SUM(quantityOnHand * ISNULL(unitCost, 0)) as totalValue,
                AVG(CASE WHEN quantityAvailable > 0 THEN quantityAvailable ELSE NULL END) as avgStockLevel
            FROM Inventory
            WHERE isActive = 1
        `);

        const topProductsResult = await pool.request().query(`
            SELECT TOP 10
                p.name as productName,
                SUM(oi.quantity) as totalSold,
                SUM(oi.totalPrice) as totalRevenue
            FROM OrderItems oi
            INNER JOIN Products p ON oi.productId = p.id
            INNER JOIN Orders o ON oi.orderId = o.id
            WHERE o.createdAt >= DATEADD(month, -1, GETDATE())
            AND o.status NOT IN ('cancelled', 'returned')
            GROUP BY p.id, p.name
            ORDER BY totalSold DESC
        `);

        const lowStockResult = await pool.request().query(`
            SELECT TOP 10
                i.sku,
                COALESCE(p.name, pv.variantName) as itemName,
                i.quantityAvailable,
                i.reorderLevel
            FROM Inventory i
            LEFT JOIN Products p ON i.productId = p.id
            LEFT JOIN ProductVariants pv ON i.variantId = pv.id
            WHERE i.quantityAvailable <= i.reorderLevel
            AND i.isActive = 1
            ORDER BY (i.quantityAvailable - i.reorderLevel) ASC
        `);

        res.json({
            summary: result.recordset[0],
            topProducts: topProductsResult.recordset,
            lowStockItems: lowStockResult.recordset
        });
    } catch (error) {
        console.error('Get inventory dashboard error:', error);
        res.status(500).json({
            message: 'Failed to retrieve inventory dashboard data',
            code: 'DASHBOARD_FETCH_ERROR'
        });
    }
};

module.exports = {
    getAllInventory,
    getInventoryById,
    createInventory,
    updateInventory,
    getStockMovements,
    getInventoryDashboard
};
