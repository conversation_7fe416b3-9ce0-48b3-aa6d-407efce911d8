import api from './api';

// Mock data for when backend is not available
const mockProducts = [
    {
        id: 1,
        name: 'Executive Mahogany Desk',
        description: 'Premium executive desk crafted from solid mahogany wood with leather inlay writing surface. Features multiple drawers with soft-close mechanisms and cable management system.',
        price: 2499.99,
        discountPrice: 2199.99,
        categoryName: 'Office Desks',
        images: [
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
            'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800'
        ],
        model3D: '/models/executive-desk.glb',
        specifications: {
            material: 'Solid Mahogany',
            dimensions: '72x36x30 inches',
            weight: '180 lbs',
            finish: 'Hand-rubbed lacquer',
            drawers: 7,
            warranty: '10 years'
        },
        featured: true
    },
    {
        id: 2,
        name: 'Modern Glass Executive Desk',
        description: 'Contemporary glass-top executive desk with chrome legs and built-in storage. Perfect for modern office environments.',
        price: 1899.99,
        discountPrice: null,
        categoryName: 'Office Desks',
        images: [
            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800',
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'
        ],
        model3D: '/models/glass-desk.glb',
        specifications: {
            material: 'Tempered Glass & Chrome',
            dimensions: '60x30x29 inches',
            weight: '120 lbs',
            finish: 'Polished Chrome',
            storage: '2 drawers',
            warranty: '5 years'
        },
        featured: true
    },
    {
        id: 3,
        name: 'Ergonomic Executive Chair',
        description: 'Premium leather executive chair with advanced ergonomic features, lumbar support, and adjustable armrests.',
        price: 899.99,
        discountPrice: 799.99,
        categoryName: 'Office Chairs',
        images: [
            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800',
            'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800'
        ],
        model3D: '/models/executive-chair.glb',
        specifications: {
            material: 'Genuine Leather',
            weight_capacity: '300 lbs',
            adjustments: 'Height, tilt, armrests',
            warranty: '7 years',
            color_options: ['Black', 'Brown', 'Burgundy']
        },
        featured: true
    },
    {
        id: 4,
        name: 'Standing Desk Converter',
        description: 'Adjustable standing desk converter that transforms any desk into a sit-stand workstation. Easy height adjustment mechanism.',
        price: 449.99,
        discountPrice: 399.99,
        categoryName: 'Office Desks',
        images: [
            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800'
        ],
        model3D: '/models/standing-desk.glb',
        specifications: {
            material: 'Steel & Wood',
            dimensions: '32x22x20 inches',
            weight: '35 lbs',
            height_range: '6-20 inches',
            warranty: '3 years'
        },
        featured: false
    },
    {
        id: 5,
        name: 'Executive Filing Cabinet',
        description: 'Four-drawer filing cabinet with lock mechanism and full-extension drawers. Matches executive desk collection.',
        price: 599.99,
        discountPrice: null,
        categoryName: 'Storage Solutions',
        images: [
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'
        ],
        model3D: '/models/filing-cabinet.glb',
        specifications: {
            material: 'Solid Wood',
            dimensions: '15x24x52 inches',
            drawers: 4,
            lock: 'Central locking system',
            warranty: '5 years'
        },
        featured: false
    },
    {
        id: 6,
        name: 'Modern Bookshelf Unit',
        description: 'Contemporary 5-shelf bookcase perfect for displaying books, awards, and office decor. Adjustable shelves.',
        price: 349.99,
        discountPrice: 299.99,
        categoryName: 'Storage Solutions',
        images: [
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'
        ],
        model3D: '/models/bookshelf.glb',
        specifications: {
            material: 'Engineered Wood',
            dimensions: '30x12x72 inches',
            shelves: 5,
            adjustable: true,
            warranty: '3 years'
        },
        featured: true
    },
    {
        id: 7,
        name: 'Mesh Task Chair',
        description: 'Breathable mesh office chair with lumbar support and adjustable height. Perfect for long work sessions.',
        price: 299.99,
        discountPrice: 249.99,
        categoryName: 'Office Chairs',
        images: [
            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800'
        ],
        model3D: '/models/mesh-chair.glb',
        specifications: {
            material: 'Mesh & Plastic',
            weight_capacity: '250 lbs',
            adjustments: 'Height, tilt',
            warranty: '3 years'
        },
        featured: false
    },
    {
        id: 8,
        name: 'Conference Table',
        description: 'Large oval conference table for 8-10 people. Solid wood construction with cable management.',
        price: 1899.99,
        discountPrice: null,
        categoryName: 'Conference',
        images: [
            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800'
        ],
        model3D: '/models/conference-table.glb',
        specifications: {
            material: 'Solid Oak',
            dimensions: '96x48x30 inches',
            seating: '8-10 people',
            warranty: '7 years'
        },
        featured: false
    },
    {
        id: 9,
        name: 'Desk Organizer Set',
        description: 'Complete desk organizer set with pen holders, paper trays, and document sorters.',
        price: 89.99,
        discountPrice: 69.99,
        categoryName: 'Accessories',
        images: [
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'
        ],
        model3D: '/models/desk-organizer.glb',
        specifications: {
            material: 'Bamboo',
            pieces: 5,
            warranty: '1 year'
        },
        featured: false
    },
    {
        id: 10,
        name: 'L-Shaped Computer Desk',
        description: 'Spacious L-shaped desk perfect for corner setups. Multiple shelves and keyboard tray included.',
        price: 699.99,
        discountPrice: 599.99,
        categoryName: 'Office Desks',
        images: [
            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800'
        ],
        model3D: '/models/l-shaped-desk.glb',
        specifications: {
            material: 'Engineered Wood',
            dimensions: '60x60x30 inches',
            shelves: 4,
            warranty: '3 years'
        },
        featured: false
    },
    {
        id: 11,
        name: 'Modern Mesh Task Chair',
        description: 'Professional mesh office chair with ergonomic design, adjustable lumbar support, and breathable mesh back. Features height adjustment, 360-degree swivel, and smooth-rolling casters. Perfect for long work sessions with superior comfort and support.',
        price: 349.99,
        discountPrice: 299.99,
        categoryName: 'Office Chairs',
        images: [
            'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800',
            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800',
            'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800'
        ],
        model3D: '/models/S-407Chair.glb',
        specifications: {
            material: 'Breathable Mesh & Fabric',
            weight_capacity: '275 lbs',
            seat_height: '17-21 inches',
            seat_width: '20 inches',
            seat_depth: '19 inches',
            back_height: '26 inches',
            adjustments: 'Height, tilt tension, lumbar support',
            armrests: 'Fixed padded armrests',
            base: '5-star nylon base with dual-wheel casters',
            warranty: '5 years',
            color_options: ['Black', 'Gray', 'Blue', 'Red'],
            certifications: 'GREENGUARD Gold, BIFMA'
        },
        featured: true
    }
];

const mockCategories = [
    { id: 1, name: 'Office Desks', description: 'Professional office desks and workstations' },
    { id: 2, name: 'Office Chairs', description: 'Ergonomic and executive office chairs' },
    { id: 3, name: 'Storage', description: 'Filing cabinets, bookcases, and storage units' },
    { id: 4, name: 'Conference', description: 'Conference tables and meeting room furniture' },
    { id: 5, name: 'Accessories', description: 'Office accessories and organizational tools' }
];

// Helper function to simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

export const productService = {
    async getAllProducts(params = {}) {
        // Using mock data only (no backend)
        await delay(500);

        const { page = 1, limit = 10, category, search } = params;
        let filteredProducts = [...mockProducts];

        // Apply filters
        if (category) {
            filteredProducts = filteredProducts.filter(p =>
                p.categoryName.toLowerCase().includes(category.toLowerCase())
            );
        }

        if (search) {
            filteredProducts = filteredProducts.filter(p =>
                p.name.toLowerCase().includes(search.toLowerCase()) ||
                p.description.toLowerCase().includes(search.toLowerCase())
            );
        }

        // Apply pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

        return {
            products: paginatedProducts,
            total: filteredProducts.length,
            page: parseInt(page),
            totalPages: Math.ceil(filteredProducts.length / limit)
        };
    },

    async getProductById(id) {
        // Using mock data only (no backend)
        await delay(300);

        const product = mockProducts.find(p => p.id === parseInt(id));
        if (!product) {
            throw new Error('Product not found');
        }

        return { product };
    },

    async getFeaturedProducts(limit = 8) {
        // Using mock data only (no backend)
        await delay(400);

        const featuredProducts = mockProducts.filter(p => p.featured);
        return { products: featuredProducts };
    },

    async getCategories() {
        // Using mock data only (no backend)
        await delay(200);

        return { categories: mockCategories };
    }
};

// Export individual functions for easier imports
export const getAllProducts = productService.getAllProducts;
export const getProductById = productService.getProductById;
export const getFeaturedProducts = productService.getFeaturedProducts;
export const getCategories = productService.getCategories;
