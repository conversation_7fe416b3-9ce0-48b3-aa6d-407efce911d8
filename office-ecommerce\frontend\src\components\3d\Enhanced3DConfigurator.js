import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import GLBLoader from './GLBLoader';
import '../styles/configurator.css';

const Enhanced3DConfigurator = ({ onBack, product }) => {
  // 3D Scene refs
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const productRef = useRef(null);
  const animationIdRef = useRef(null);
  const cameraRef = useRef(null);

  // State
  const [useGLB, setUseGLB] = useState(true); // Toggle between GLB and procedural
  const [glbLoading, setGlbLoading] = useState(false);
  const [glbError, setGlbError] = useState(null);
  const [loadingProgress, setLoadingProgress] = useState(0);

  // Configuration state
  const [dimensions, setDimensions] = useState({
    width: 120,
    depth: 80,
    height: 75
  });

  const [colors, setColors] = useState({
    primary: '#8B4513',
    secondary: '#654321'
  });

  const [material, setMaterial] = useState('wood');
  const [quantity, setQuantity] = useState(1);

  // Mobile detection
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // GLB Model Loading
  const handleGLBLoad = (model, gltf) => {
    if (sceneRef.current && productRef.current) {
      // Remove existing product
      sceneRef.current.remove(productRef.current);
    }

    // Add new GLB model
    productRef.current = model;
    sceneRef.current.add(model);
    
    console.log('GLB model loaded successfully:', gltf);
  };

  const handleGLBError = (error) => {
    console.error('Failed to load GLB model:', error);
    setGlbError(error);
    // Fallback to procedural model
    setUseGLB(false);
  };

  const handleGLBProgress = (progress) => {
    setLoadingProgress(progress);
  };

  // Procedural model creation (fallback)
  const createProceduralModel = () => {
    const group = new THREE.Group();

    // Create a simple table as example
    const topGeometry = new THREE.BoxGeometry(
      dimensions.width / 100,
      0.08,
      dimensions.depth / 100
    );
    const topMaterial = new THREE.MeshStandardMaterial({
      color: colors.primary,
      roughness: 0.7
    });

    const tableTop = new THREE.Mesh(topGeometry, topMaterial);
    tableTop.position.y = dimensions.height / 100 - 0.04;
    tableTop.castShadow = true;
    tableTop.receiveShadow = true;
    group.add(tableTop);

    // Add legs
    const legGeometry = new THREE.BoxGeometry(0.05, dimensions.height / 100 - 0.08, 0.05);
    const legMaterial = new THREE.MeshStandardMaterial({
      color: colors.secondary,
      roughness: 0.8
    });

    const legPositions = [
      [-dimensions.width / 200 + 0.025, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.025],
      [dimensions.width / 200 - 0.025, (dimensions.height / 100 - 0.08) / 2, -dimensions.depth / 200 + 0.025],
      [-dimensions.width / 200 + 0.025, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.025],
      [dimensions.width / 200 - 0.025, (dimensions.height / 100 - 0.08) / 2, dimensions.depth / 200 - 0.025]
    ];

    legPositions.forEach(pos => {
      const leg = new THREE.Mesh(legGeometry, legMaterial);
      leg.position.set(...pos);
      leg.castShadow = true;
      group.add(leg);
    });

    return group;
  };

  // 3D Scene Setup
  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf5f5f5);
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      mountRef.current.clientWidth / mountRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(4, 3, 4);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: !isMobile,
      alpha: true
    });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
    renderer.shadowMap.enabled = !isMobile;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    if (!isMobile) {
      directionalLight.castShadow = true;
      directionalLight.shadow.mapSize.width = 2048;
      directionalLight.shadow.mapSize.height = 2048;
    }
    scene.add(directionalLight);

    // Ground plane
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshStandardMaterial({
      color: 0xffffff,
      roughness: 1.0
    });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.position.y = -0.1;
    if (!isMobile) {
      ground.receiveShadow = true;
    }
    scene.add(ground);

    // Add to DOM
    mountRef.current.appendChild(renderer.domElement);

    // Animation loop
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate);
      renderer.render(scene, camera);
    };
    animate();

    // Handle resize
    const handleResize = () => {
      if (mountRef.current) {
        camera.aspect = mountRef.current.clientWidth / mountRef.current.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
      }
    };
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
      window.removeEventListener('resize', handleResize);
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
    };
  }, [isMobile]);

  // Load model based on type
  useEffect(() => {
    if (!sceneRef.current) return;

    if (useGLB && product?.model3D) {
      setGlbLoading(true);
      setGlbError(null);
    } else {
      // Use procedural model
      if (productRef.current) {
        sceneRef.current.remove(productRef.current);
      }
      const proceduralModel = createProceduralModel();
      productRef.current = proceduralModel;
      sceneRef.current.add(proceduralModel);
    }
  }, [useGLB, product, dimensions, colors, material]);

  const toggleModelType = () => {
    setUseGLB(!useGLB);
  };

  return (
    <div className="configurator-container">
      {/* Header */}
      <div className="configurator-header">
        <button onClick={onBack} className="back-button">
          ← Back to Product
        </button>
        <h2>3D Configurator - {product?.name || 'Product'}</h2>
        <button onClick={toggleModelType} className="toggle-button">
          {useGLB ? 'Use Procedural' : 'Use GLB Model'}
        </button>
      </div>

      {/* Main Content */}
      <div className="configurator-main">
        <div className="container">
          <div className="configurator-layout-horizontal">
            {/* 3D Viewer */}
            <div className="viewer-panel">
              <div className="config-card viewer-card">
                <div className="card-header">
                  <h4>3D Preview</h4>
                  <p>Model Type: {useGLB ? 'GLB File' : 'Procedural'}</p>
                </div>
                <div className="model-viewer-container">
                  <div ref={mountRef} className="model-viewer" />
                  
                  {/* Loading indicator */}
                  {glbLoading && (
                    <div className="loading-overlay">
                      <div className="loading-spinner"></div>
                      <p>Loading 3D Model... {Math.round(loadingProgress)}%</p>
                    </div>
                  )}
                  
                  {/* Error indicator */}
                  {glbError && (
                    <div className="error-overlay">
                      <p>Failed to load GLB model</p>
                      <button onClick={() => setUseGLB(false)}>
                        Use Procedural Model
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Configuration Panel */}
            <div className="config-panel">
              <div className="config-card">
                <div className="card-header">
                  <h4>Configuration</h4>
                </div>
                <div className="config-content">
                  {/* Dimensions */}
                  <div className="config-section">
                    <h5>Dimensions (cm)</h5>
                    <div className="dimension-controls">
                      <label>
                        Width: {dimensions.width}cm
                        <input
                          type="range"
                          min="80"
                          max="200"
                          value={dimensions.width}
                          onChange={(e) => setDimensions(prev => ({
                            ...prev,
                            width: parseInt(e.target.value)
                          }))}
                        />
                      </label>
                      <label>
                        Depth: {dimensions.depth}cm
                        <input
                          type="range"
                          min="60"
                          max="120"
                          value={dimensions.depth}
                          onChange={(e) => setDimensions(prev => ({
                            ...prev,
                            depth: parseInt(e.target.value)
                          }))}
                        />
                      </label>
                      <label>
                        Height: {dimensions.height}cm
                        <input
                          type="range"
                          min="60"
                          max="90"
                          value={dimensions.height}
                          onChange={(e) => setDimensions(prev => ({
                            ...prev,
                            height: parseInt(e.target.value)
                          }))}
                        />
                      </label>
                    </div>
                  </div>

                  {/* Colors */}
                  <div className="config-section">
                    <h5>Colors</h5>
                    <div className="color-controls">
                      <label>
                        Primary Color:
                        <input
                          type="color"
                          value={colors.primary}
                          onChange={(e) => setColors(prev => ({
                            ...prev,
                            primary: e.target.value
                          }))}
                        />
                      </label>
                      <label>
                        Secondary Color:
                        <input
                          type="color"
                          value={colors.secondary}
                          onChange={(e) => setColors(prev => ({
                            ...prev,
                            secondary: e.target.value
                          }))}
                        />
                      </label>
                    </div>
                  </div>

                  {/* Material */}
                  <div className="config-section">
                    <h5>Material</h5>
                    <select
                      value={material}
                      onChange={(e) => setMaterial(e.target.value)}
                    >
                      <option value="wood">Wood</option>
                      <option value="metal">Metal</option>
                      <option value="glass">Glass</option>
                      <option value="plastic">Plastic</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* GLB Loader Component */}
      {useGLB && product?.model3D && (
        <GLBLoader
          modelPath={product.model3D}
          onLoad={handleGLBLoad}
          onError={handleGLBError}
          onProgress={handleGLBProgress}
          scale={1}
          position={[0, 0, 0]}
          rotation={[0, 0, 0]}
        />
      )}
    </div>
  );
};

export default Enhanced3DConfigurator;
