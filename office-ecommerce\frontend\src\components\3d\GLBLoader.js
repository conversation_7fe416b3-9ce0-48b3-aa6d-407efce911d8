import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

const GLBLoader = ({ 
  modelPath, 
  onLoad, 
  onError, 
  onProgress,
  scale = 1,
  position = [0, 0, 0],
  rotation = [0, 0, 0]
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const modelRef = useRef(null);

  useEffect(() => {
    if (!modelPath) return;

    const loader = new GLTFLoader();
    setLoading(true);
    setError(null);

    loader.load(
      modelPath,
      // onLoad
      (gltf) => {
        const model = gltf.scene;
        
        // Apply transformations
        model.scale.setScalar(scale);
        model.position.set(...position);
        model.rotation.set(...rotation);

        // Enable shadows
        model.traverse((child) => {
          if (child.isMesh) {
            child.castShadow = true;
            child.receiveShadow = true;
            
            // Ensure materials are properly set up
            if (child.material) {
              child.material.needsUpdate = true;
            }
          }
        });

        modelRef.current = model;
        setLoading(false);
        
        if (onLoad) {
          onLoad(model, gltf);
        }
      },
      // onProgress
      (progress) => {
        if (onProgress) {
          const percentComplete = (progress.loaded / progress.total) * 100;
          onProgress(percentComplete);
        }
      },
      // onError
      (error) => {
        console.error('Error loading GLB model:', error);
        setError(error);
        setLoading(false);
        
        if (onError) {
          onError(error);
        }
      }
    );

    // Cleanup
    return () => {
      if (modelRef.current) {
        // Dispose of geometries and materials
        modelRef.current.traverse((child) => {
          if (child.isMesh) {
            if (child.geometry) {
              child.geometry.dispose();
            }
            if (child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach(material => material.dispose());
              } else {
                child.material.dispose();
              }
            }
          }
        });
      }
    };
  }, [modelPath, scale, position, rotation]);

  return {
    model: modelRef.current,
    loading,
    error
  };
};

export default GLBLoader;
