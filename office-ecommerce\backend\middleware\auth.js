const jwt = require('jsonwebtoken');
const { getPool } = require('../config/database');

const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({
                message: 'Access token required',
                code: 'TOKEN_REQUIRED'
            });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Get user from database to ensure they still exist
        const pool = getPool();
        const result = await pool.request()
            .input('userId', decoded.userId)
            .query(`
                SELECT id, email, firstName, lastName, isActive, createdAt
                FROM Users
                WHERE id = @userId AND isActive = 1
            `);

        if (result.recordset.length === 0) {
            return res.status(401).json({
                message: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }

        req.user = result.recordset[0];
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                message: 'Invalid token',
                code: 'INVALID_TOKEN'
            });
        }

        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                message: 'Token expired',
                code: 'TOKEN_EXPIRED'
            });
        }

        console.error('Auth middleware error:', error);
        res.status(500).json({
            message: 'Authentication error',
            code: 'AUTH_ERROR'
        });
    }
};

const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            req.user = null;
            return next();
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        const pool = getPool();
        const result = await pool.request()
            .input('userId', decoded.userId)
            .query(`
                SELECT id, email, firstName, lastName, isActive, createdAt
                FROM Users
                WHERE id = @userId AND isActive = 1
            `);

        req.user = result.recordset.length > 0 ? result.recordset[0] : null;
        next();
    } catch (error) {
        req.user = null;
        next();
    }
};

module.exports = {
    authenticateToken,
    optionalAuth
};
