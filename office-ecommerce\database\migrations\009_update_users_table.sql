USE OfficeEcommerce;
GO

-- Add role-related columns to Users table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Users') AND name = 'isActive')
BEGIN
    ALTER TABLE Users ADD 
        isActive BIT DEFAULT 1,
        lastLoginAt DATETIME NULL,
        loginAttempts INT DEFAULT 0,
        lockedUntil DATETIME NULL,
        emailVerified BIT DEFAULT 0,
        emailVerificationToken NVARCHAR(255) NULL,
        passwordResetToken NVARCHAR(255) NULL,
        passwordResetExpires DATETIME NULL,
        preferences NVARCHAR(MAX) NULL, -- JSON for user preferences
        avatar NVARCHAR(255) NULL,
        timezone NVARCHAR(50) DEFAULT 'UTC',
        language NVARCHAR(10) DEFAULT 'en',
        currency NVARCHAR(10) DEFAULT 'USD';

    -- Create indexes for new columns
    CREATE INDEX IX_Users_IsActive ON Users(isActive);
    CREATE INDEX IX_Users_LastLoginAt ON Users(lastLoginAt);
    CREATE INDEX IX_Users_EmailVerified ON Users(emailVerified);
    CREATE INDEX IX_Users_EmailVerificationToken ON Users(emailVerificationToken);
    CREATE INDEX IX_Users_PasswordResetToken ON Users(passwordResetToken);

    PRINT 'Added new columns to Users table';
END
ELSE
BEGIN
    PRINT 'Users table already has the new columns';
END
GO

-- Create default admin user if not exists
IF NOT EXISTS (SELECT * FROM Users WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO Users (email, password, firstName, lastName, isActive, emailVerified)
    VALUES ('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', 1, 1);
    
    DECLARE @adminUserId INT = SCOPE_IDENTITY();
    DECLARE @adminRoleId INT = (SELECT id FROM Roles WHERE name = 'Admin');
    
    -- Assign admin role to the admin user
    INSERT INTO UserRoles (userId, roleId, assignedBy)
    VALUES (@adminUserId, @adminRoleId, @adminUserId);
    
    PRINT 'Created default admin user';
END
ELSE
BEGIN
    PRINT 'Admin user already exists';
END
GO

-- Create default employee user if not exists
IF NOT EXISTS (SELECT * FROM Users WHERE email = '<EMAIL>')
BEGIN
    INSERT INTO Users (email, password, firstName, lastName, isActive, emailVerified)
    VALUES ('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Demo', 'Employee', 1, 1);
    
    DECLARE @employeeUserId INT = SCOPE_IDENTITY();
    DECLARE @employeeRoleId INT = (SELECT id FROM Roles WHERE name = 'Employee');
    
    -- Assign employee role to the employee user
    INSERT INTO UserRoles (userId, roleId, assignedBy)
    VALUES (@employeeUserId, @employeeRoleId, 1); -- Assigned by admin
    
    PRINT 'Created default employee user';
END
ELSE
BEGIN
    PRINT 'Employee user already exists';
END
GO

-- Update existing users to have Customer role if they don't have any role
INSERT INTO UserRoles (userId, roleId, assignedBy)
SELECT u.id, r.id, 1
FROM Users u
CROSS JOIN Roles r
WHERE r.name = 'Customer'
AND u.id NOT IN (SELECT userId FROM UserRoles)
AND u.email NOT IN ('<EMAIL>', '<EMAIL>');

PRINT 'Assigned Customer role to existing users without roles';
GO

-- Create stored procedure for user role management
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetUserPermissions')
    DROP PROCEDURE sp_GetUserPermissions;
GO

CREATE PROCEDURE sp_GetUserPermissions
    @UserId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT DISTINCT p.name as permissionName, p.module, p.action
    FROM Users u
    INNER JOIN UserRoles ur ON u.id = ur.userId
    INNER JOIN Roles r ON ur.roleId = r.id
    INNER JOIN RolePermissions rp ON r.id = rp.roleId
    INNER JOIN Permissions p ON rp.permissionId = p.id
    WHERE u.id = @UserId 
    AND u.isActive = 1 
    AND ur.isActive = 1 
    AND r.isActive = 1;
END
GO

-- Create stored procedure for checking user permissions
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_CheckUserPermission')
    DROP PROCEDURE sp_CheckUserPermission;
GO

CREATE PROCEDURE sp_CheckUserPermission
    @UserId INT,
    @PermissionName NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @HasPermission BIT = 0;
    
    IF EXISTS (
        SELECT 1
        FROM Users u
        INNER JOIN UserRoles ur ON u.id = ur.userId
        INNER JOIN Roles r ON ur.roleId = r.id
        INNER JOIN RolePermissions rp ON r.id = rp.roleId
        INNER JOIN Permissions p ON rp.permissionId = p.id
        WHERE u.id = @UserId 
        AND p.name = @PermissionName
        AND u.isActive = 1 
        AND ur.isActive = 1 
        AND r.isActive = 1
    )
    BEGIN
        SET @HasPermission = 1;
    END
    
    SELECT @HasPermission as hasPermission;
END
GO

PRINT 'User management stored procedures created successfully';
GO
