const sql = require('mssql');
require('dotenv').config();

const connectionTests = [
    {
        name: "Default SQL Server Instance (Windows Auth)",
        config: {
            server: 'localhost',
            database: 'master',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                integratedSecurity: true
            }
        }
    },
    {
        name: "SQL Express Instance (Windows Auth)",
        config: {
            server: 'localhost\\SQLEXPRESS',
            database: 'master',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                integratedSecurity: true
            }
        }
    },
    {
        name: "SQL Express Instance (Named Pipes)",
        config: {
            server: '(local)\\SQLEXPRESS',
            database: 'master',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                integratedSecurity: true
            }
        }
    },
    {
        name: "SQL Express Instance (Local DB)",
        config: {
            server: '(localdb)\\MSSQLLocalDB',
            database: 'master',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                integratedSecurity: true
            }
        }
    },
    {
        name: "Default Instance with SA (if enabled)",
        config: {
            server: 'localhost',
            database: 'master',
            user: 'sa',
            password: 'YourPassword123!',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true
            }
        }
    },
    {
        name: "SQL Express with SA (if enabled)",
        config: {
            server: 'localhost\\SQLEXPRESS',
            database: 'master',
            user: 'sa',
            password: 'YourPassword123!',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true
            }
        }
    }
];

const testConnection = async (testConfig) => {
    let pool;
    try {
        console.log(`\n🔄 Testing: ${testConfig.name}`);
        console.log(`   Server: ${testConfig.config.server}`);
        console.log(`   Auth: ${testConfig.config.options?.integratedSecurity ? 'Windows' : 'SQL Server'}`);
        
        pool = await sql.connect(testConfig.config);
        
        // Test with a simple query
        const result = await pool.request().query('SELECT @@VERSION as version, @@SERVERNAME as serverName');
        
        console.log(`✅ SUCCESS! Connected to: ${testConfig.name}`);
        console.log(`   Server Name: ${result.recordset[0].serverName}`);
        console.log(`   Version: ${result.recordset[0].version.split('\n')[0]}`);
        
        await pool.close();
        return testConfig;
        
    } catch (error) {
        console.log(`❌ FAILED: ${error.message}`);
        if (pool) {
            try {
                await pool.close();
            } catch (closeError) {
                // Ignore close errors
            }
        }
        return null;
    }
};

const runConnectionTests = async () => {
    console.log('🔍 Testing SQL Server Connection Methods...\n');
    console.log('=' * 60);
    
    const workingConnections = [];
    
    for (const testConfig of connectionTests) {
        const result = await testConnection(testConfig);
        if (result) {
            workingConnections.push(result);
        }
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n' + '=' * 60);
    console.log('📊 SUMMARY:');
    
    if (workingConnections.length === 0) {
        console.log('❌ No working connections found!');
        console.log('\n💡 Troubleshooting suggestions:');
        console.log('1. Ensure SQL Server is running');
        console.log('2. Check if TCP/IP is enabled in SQL Server Configuration Manager');
        console.log('3. Verify Windows Authentication is working');
        console.log('4. Check if SQL Server Authentication is enabled (if using SA)');
        console.log('5. Ensure firewall is not blocking connections');
        console.log('6. Try running this script as Administrator');
    } else {
        console.log(`✅ Found ${workingConnections.length} working connection(s):`);
        workingConnections.forEach((conn, index) => {
            console.log(`   ${index + 1}. ${conn.name}`);
        });
        
        console.log('\n🔧 Recommended .env configuration:');
        const recommended = workingConnections[0];
        console.log(`DB_SERVER=${recommended.config.server}`);
        console.log(`DB_DATABASE=OfficeEcommerce`);
        if (recommended.config.options?.integratedSecurity) {
            console.log('DB_USER=');
            console.log('DB_PASSWORD=');
            console.log('DB_WINDOWS_AUTH=true');
        } else {
            console.log(`DB_USER=${recommended.config.user || 'sa'}`);
            console.log(`DB_PASSWORD=${recommended.config.password || 'YourPassword123!'}`);
            console.log('DB_WINDOWS_AUTH=false');
        }
        console.log('DB_ENCRYPT=false');
        console.log('DB_TRUST_CERT=true');
    }
};

// Run the tests
runConnectionTests().catch(console.error);
