{"ast": null, "code": "import api from './api';\n\n// Mock data for when backend is not available\nconst mockProducts = [{\n  id: 1,\n  name: 'Executive Mahogany Desk',\n  description: 'Premium executive desk crafted from solid mahogany wood with leather inlay writing surface. Features multiple drawers with soft-close mechanisms and cable management system.',\n  price: 2499.99,\n  discountPrice: 2199.99,\n  categoryName: 'Office Desks',\n  images: ['https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800', 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800'],\n  model3D: '/models/executive-desk.glb',\n  specifications: {\n    material: 'Solid Mahogany',\n    dimensions: '72x36x30 inches',\n    weight: '180 lbs',\n    finish: 'Hand-rubbed lacquer',\n    drawers: 7,\n    warranty: '10 years'\n  },\n  featured: true\n}, {\n  id: 2,\n  name: 'Modern Glass Executive Desk',\n  description: 'Contemporary glass-top executive desk with chrome legs and built-in storage. Perfect for modern office environments.',\n  price: 1899.99,\n  discountPrice: null,\n  categoryName: 'Office Desks',\n  images: ['https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'],\n  model3D: '/models/glass-desk.glb',\n  specifications: {\n    material: 'Tempered Glass & Chrome',\n    dimensions: '60x30x29 inches',\n    weight: '120 lbs',\n    finish: 'Polished Chrome',\n    storage: '2 drawers',\n    warranty: '5 years'\n  },\n  featured: true\n}, {\n  id: 3,\n  name: 'Ergonomic Executive Chair',\n  description: 'Premium leather executive chair with advanced ergonomic features, lumbar support, and adjustable armrests.',\n  price: 899.99,\n  discountPrice: 799.99,\n  categoryName: 'Office Chairs',\n  images: ['https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800', 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800'],\n  model3D: '/models/executive-chair.glb',\n  specifications: {\n    material: 'Genuine Leather',\n    weight_capacity: '300 lbs',\n    adjustments: 'Height, tilt, armrests',\n    warranty: '7 years',\n    color_options: ['Black', 'Brown', 'Burgundy']\n  },\n  featured: true\n}, {\n  id: 4,\n  name: 'Standing Desk Converter',\n  description: 'Adjustable standing desk converter that transforms any desk into a sit-stand workstation. Easy height adjustment mechanism.',\n  price: 449.99,\n  discountPrice: 399.99,\n  categoryName: 'Office Desks',\n  images: ['https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800'],\n  model3D: '/models/standing-desk.glb',\n  specifications: {\n    material: 'Steel & Wood',\n    dimensions: '32x22x20 inches',\n    weight: '35 lbs',\n    height_range: '6-20 inches',\n    warranty: '3 years'\n  },\n  featured: false\n}, {\n  id: 5,\n  name: 'Executive Filing Cabinet',\n  description: 'Four-drawer filing cabinet with lock mechanism and full-extension drawers. Matches executive desk collection.',\n  price: 599.99,\n  discountPrice: null,\n  categoryName: 'Storage Solutions',\n  images: ['https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'],\n  model3D: '/models/filing-cabinet.glb',\n  specifications: {\n    material: 'Solid Wood',\n    dimensions: '15x24x52 inches',\n    drawers: 4,\n    lock: 'Central locking system',\n    warranty: '5 years'\n  },\n  featured: false\n}, {\n  id: 6,\n  name: 'Modern Bookshelf Unit',\n  description: 'Contemporary 5-shelf bookcase perfect for displaying books, awards, and office decor. Adjustable shelves.',\n  price: 349.99,\n  discountPrice: 299.99,\n  categoryName: 'Storage Solutions',\n  images: ['https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'],\n  model3D: '/models/bookshelf.glb',\n  specifications: {\n    material: 'Engineered Wood',\n    dimensions: '30x12x72 inches',\n    shelves: 5,\n    adjustable: true,\n    warranty: '3 years'\n  },\n  featured: true\n}, {\n  id: 7,\n  name: 'Mesh Task Chair',\n  description: 'Breathable mesh office chair with lumbar support and adjustable height. Perfect for long work sessions.',\n  price: 299.99,\n  discountPrice: 249.99,\n  categoryName: 'Office Chairs',\n  images: ['https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800'],\n  model3D: '/models/mesh-chair.glb',\n  specifications: {\n    material: 'Mesh & Plastic',\n    weight_capacity: '250 lbs',\n    adjustments: 'Height, tilt',\n    warranty: '3 years'\n  },\n  featured: false\n}, {\n  id: 8,\n  name: 'Conference Table',\n  description: 'Large oval conference table for 8-10 people. Solid wood construction with cable management.',\n  price: 1899.99,\n  discountPrice: null,\n  categoryName: 'Conference',\n  images: ['https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800'],\n  model3D: '/models/conference-table.glb',\n  specifications: {\n    material: 'Solid Oak',\n    dimensions: '96x48x30 inches',\n    seating: '8-10 people',\n    warranty: '7 years'\n  },\n  featured: false\n}, {\n  id: 9,\n  name: 'Desk Organizer Set',\n  description: 'Complete desk organizer set with pen holders, paper trays, and document sorters.',\n  price: 89.99,\n  discountPrice: 69.99,\n  categoryName: 'Accessories',\n  images: ['https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'],\n  model3D: '/models/desk-organizer.glb',\n  specifications: {\n    material: 'Bamboo',\n    pieces: 5,\n    warranty: '1 year'\n  },\n  featured: false\n}, {\n  id: 10,\n  name: 'L-Shaped Computer Desk',\n  description: 'Spacious L-shaped desk perfect for corner setups. Multiple shelves and keyboard tray included.',\n  price: 699.99,\n  discountPrice: 599.99,\n  categoryName: 'Office Desks',\n  images: ['https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800'],\n  model3D: '/models/l-shaped-desk.glb',\n  specifications: {\n    material: 'Engineered Wood',\n    dimensions: '60x60x30 inches',\n    shelves: 4,\n    warranty: '3 years'\n  },\n  featured: false\n}, {\n  id: 11,\n  name: 'Modern Mesh Task Chair',\n  description: 'Professional mesh office chair with ergonomic design, adjustable lumbar support, and breathable mesh back. Features height adjustment, 360-degree swivel, and smooth-rolling casters. Perfect for long work sessions with superior comfort and support.',\n  price: 349.99,\n  discountPrice: 299.99,\n  categoryName: 'Office Chairs',\n  images: ['https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800', 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800', 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800'],\n  model3D: '/models/S-407Chair.gltf',\n  specifications: {\n    material: 'Breathable Mesh & Fabric',\n    weight_capacity: '275 lbs',\n    seat_height: '17-21 inches',\n    seat_width: '20 inches',\n    seat_depth: '19 inches',\n    back_height: '26 inches',\n    adjustments: 'Height, tilt tension, lumbar support',\n    armrests: 'Fixed padded armrests',\n    base: '5-star nylon base with dual-wheel casters',\n    warranty: '5 years',\n    color_options: ['Black', 'Gray', 'Blue', 'Red'],\n    certifications: 'GREENGUARD Gold, BIFMA'\n  },\n  featured: true\n}];\nconst mockCategories = [{\n  id: 1,\n  name: 'Office Desks',\n  description: 'Professional office desks and workstations'\n}, {\n  id: 2,\n  name: 'Office Chairs',\n  description: 'Ergonomic and executive office chairs'\n}, {\n  id: 3,\n  name: 'Storage',\n  description: 'Filing cabinets, bookcases, and storage units'\n}, {\n  id: 4,\n  name: 'Conference',\n  description: 'Conference tables and meeting room furniture'\n}, {\n  id: 5,\n  name: 'Accessories',\n  description: 'Office accessories and organizational tools'\n}];\n\n// Helper function to simulate API delay\nconst delay = ms => new Promise(resolve => setTimeout(resolve, ms));\nexport const productService = {\n  async getAllProducts(params = {}) {\n    // Using mock data only (no backend)\n    await delay(500);\n    const {\n      page = 1,\n      limit = 10,\n      category,\n      search\n    } = params;\n    let filteredProducts = [...mockProducts];\n\n    // Apply filters\n    if (category) {\n      filteredProducts = filteredProducts.filter(p => p.categoryName.toLowerCase().includes(category.toLowerCase()));\n    }\n    if (search) {\n      filteredProducts = filteredProducts.filter(p => p.name.toLowerCase().includes(search.toLowerCase()) || p.description.toLowerCase().includes(search.toLowerCase()));\n    }\n\n    // Apply pagination\n    const startIndex = (page - 1) * limit;\n    const endIndex = startIndex + limit;\n    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);\n    return {\n      products: paginatedProducts,\n      total: filteredProducts.length,\n      page: parseInt(page),\n      totalPages: Math.ceil(filteredProducts.length / limit)\n    };\n  },\n  async getProductById(id) {\n    // Using mock data only (no backend)\n    await delay(300);\n    const product = mockProducts.find(p => p.id === parseInt(id));\n    if (!product) {\n      throw new Error('Product not found');\n    }\n    return {\n      product\n    };\n  },\n  async getFeaturedProducts(limit = 8) {\n    // Using mock data only (no backend)\n    await delay(400);\n    const featuredProducts = mockProducts.filter(p => p.featured);\n    return {\n      products: featuredProducts\n    };\n  },\n  async getCategories() {\n    // Using mock data only (no backend)\n    await delay(200);\n    return {\n      categories: mockCategories\n    };\n  }\n};\n\n// Export individual functions for easier imports\nexport const getAllProducts = productService.getAllProducts;\nexport const getProductById = productService.getProductById;\nexport const getFeaturedProducts = productService.getFeaturedProducts;\nexport const getCategories = productService.getCategories;", "map": {"version": 3, "names": ["api", "mockProducts", "id", "name", "description", "price", "discountPrice", "categoryName", "images", "model3D", "specifications", "material", "dimensions", "weight", "finish", "drawers", "warranty", "featured", "storage", "weight_capacity", "adjustments", "color_options", "height_range", "lock", "shelves", "adjustable", "seating", "pieces", "seat_height", "seat_width", "seat_depth", "back_height", "armrests", "base", "certifications", "mockCategories", "delay", "ms", "Promise", "resolve", "setTimeout", "productService", "getAllProducts", "params", "page", "limit", "category", "search", "filteredProducts", "filter", "p", "toLowerCase", "includes", "startIndex", "endIndex", "paginatedProducts", "slice", "products", "total", "length", "parseInt", "totalPages", "Math", "ceil", "getProductById", "product", "find", "Error", "getFeaturedProducts", "featuredProducts", "getCategories", "categories"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/services/products.js"], "sourcesContent": ["import api from './api';\n\n// Mock data for when backend is not available\nconst mockProducts = [\n    {\n        id: 1,\n        name: 'Executive Mahogany Desk',\n        description: 'Premium executive desk crafted from solid mahogany wood with leather inlay writing surface. Features multiple drawers with soft-close mechanisms and cable management system.',\n        price: 2499.99,\n        discountPrice: 2199.99,\n        categoryName: 'Office Desks',\n        images: [\n            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',\n            'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800'\n        ],\n        model3D: '/models/executive-desk.glb',\n        specifications: {\n            material: 'Solid Mahogany',\n            dimensions: '72x36x30 inches',\n            weight: '180 lbs',\n            finish: 'Hand-rubbed lacquer',\n            drawers: 7,\n            warranty: '10 years'\n        },\n        featured: true\n    },\n    {\n        id: 2,\n        name: 'Modern Glass Executive Desk',\n        description: 'Contemporary glass-top executive desk with chrome legs and built-in storage. Perfect for modern office environments.',\n        price: 1899.99,\n        discountPrice: null,\n        categoryName: 'Office Desks',\n        images: [\n            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800',\n            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'\n        ],\n        model3D: '/models/glass-desk.glb',\n        specifications: {\n            material: 'Tempered Glass & Chrome',\n            dimensions: '60x30x29 inches',\n            weight: '120 lbs',\n            finish: 'Polished Chrome',\n            storage: '2 drawers',\n            warranty: '5 years'\n        },\n        featured: true\n    },\n    {\n        id: 3,\n        name: 'Ergonomic Executive Chair',\n        description: 'Premium leather executive chair with advanced ergonomic features, lumbar support, and adjustable armrests.',\n        price: 899.99,\n        discountPrice: 799.99,\n        categoryName: 'Office Chairs',\n        images: [\n            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800',\n            'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800'\n        ],\n        model3D: '/models/executive-chair.glb',\n        specifications: {\n            material: 'Genuine Leather',\n            weight_capacity: '300 lbs',\n            adjustments: 'Height, tilt, armrests',\n            warranty: '7 years',\n            color_options: ['Black', 'Brown', 'Burgundy']\n        },\n        featured: true\n    },\n    {\n        id: 4,\n        name: 'Standing Desk Converter',\n        description: 'Adjustable standing desk converter that transforms any desk into a sit-stand workstation. Easy height adjustment mechanism.',\n        price: 449.99,\n        discountPrice: 399.99,\n        categoryName: 'Office Desks',\n        images: [\n            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800'\n        ],\n        model3D: '/models/standing-desk.glb',\n        specifications: {\n            material: 'Steel & Wood',\n            dimensions: '32x22x20 inches',\n            weight: '35 lbs',\n            height_range: '6-20 inches',\n            warranty: '3 years'\n        },\n        featured: false\n    },\n    {\n        id: 5,\n        name: 'Executive Filing Cabinet',\n        description: 'Four-drawer filing cabinet with lock mechanism and full-extension drawers. Matches executive desk collection.',\n        price: 599.99,\n        discountPrice: null,\n        categoryName: 'Storage Solutions',\n        images: [\n            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'\n        ],\n        model3D: '/models/filing-cabinet.glb',\n        specifications: {\n            material: 'Solid Wood',\n            dimensions: '15x24x52 inches',\n            drawers: 4,\n            lock: 'Central locking system',\n            warranty: '5 years'\n        },\n        featured: false\n    },\n    {\n        id: 6,\n        name: 'Modern Bookshelf Unit',\n        description: 'Contemporary 5-shelf bookcase perfect for displaying books, awards, and office decor. Adjustable shelves.',\n        price: 349.99,\n        discountPrice: 299.99,\n        categoryName: 'Storage Solutions',\n        images: [\n            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'\n        ],\n        model3D: '/models/bookshelf.glb',\n        specifications: {\n            material: 'Engineered Wood',\n            dimensions: '30x12x72 inches',\n            shelves: 5,\n            adjustable: true,\n            warranty: '3 years'\n        },\n        featured: true\n    },\n    {\n        id: 7,\n        name: 'Mesh Task Chair',\n        description: 'Breathable mesh office chair with lumbar support and adjustable height. Perfect for long work sessions.',\n        price: 299.99,\n        discountPrice: 249.99,\n        categoryName: 'Office Chairs',\n        images: [\n            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800'\n        ],\n        model3D: '/models/mesh-chair.glb',\n        specifications: {\n            material: 'Mesh & Plastic',\n            weight_capacity: '250 lbs',\n            adjustments: 'Height, tilt',\n            warranty: '3 years'\n        },\n        featured: false\n    },\n    {\n        id: 8,\n        name: 'Conference Table',\n        description: 'Large oval conference table for 8-10 people. Solid wood construction with cable management.',\n        price: 1899.99,\n        discountPrice: null,\n        categoryName: 'Conference',\n        images: [\n            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800'\n        ],\n        model3D: '/models/conference-table.glb',\n        specifications: {\n            material: 'Solid Oak',\n            dimensions: '96x48x30 inches',\n            seating: '8-10 people',\n            warranty: '7 years'\n        },\n        featured: false\n    },\n    {\n        id: 9,\n        name: 'Desk Organizer Set',\n        description: 'Complete desk organizer set with pen holders, paper trays, and document sorters.',\n        price: 89.99,\n        discountPrice: 69.99,\n        categoryName: 'Accessories',\n        images: [\n            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'\n        ],\n        model3D: '/models/desk-organizer.glb',\n        specifications: {\n            material: 'Bamboo',\n            pieces: 5,\n            warranty: '1 year'\n        },\n        featured: false\n    },\n    {\n        id: 10,\n        name: 'L-Shaped Computer Desk',\n        description: 'Spacious L-shaped desk perfect for corner setups. Multiple shelves and keyboard tray included.',\n        price: 699.99,\n        discountPrice: 599.99,\n        categoryName: 'Office Desks',\n        images: [\n            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800'\n        ],\n        model3D: '/models/l-shaped-desk.glb',\n        specifications: {\n            material: 'Engineered Wood',\n            dimensions: '60x60x30 inches',\n            shelves: 4,\n            warranty: '3 years'\n        },\n        featured: false\n    },\n    {\n        id: 11,\n        name: 'Modern Mesh Task Chair',\n        description: 'Professional mesh office chair with ergonomic design, adjustable lumbar support, and breathable mesh back. Features height adjustment, 360-degree swivel, and smooth-rolling casters. Perfect for long work sessions with superior comfort and support.',\n        price: 349.99,\n        discountPrice: 299.99,\n        categoryName: 'Office Chairs',\n        images: [\n            'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800',\n            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800',\n            'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800'\n        ],\n        model3D: '/models/S-407Chair.gltf',\n        specifications: {\n            material: 'Breathable Mesh & Fabric',\n            weight_capacity: '275 lbs',\n            seat_height: '17-21 inches',\n            seat_width: '20 inches',\n            seat_depth: '19 inches',\n            back_height: '26 inches',\n            adjustments: 'Height, tilt tension, lumbar support',\n            armrests: 'Fixed padded armrests',\n            base: '5-star nylon base with dual-wheel casters',\n            warranty: '5 years',\n            color_options: ['Black', 'Gray', 'Blue', 'Red'],\n            certifications: 'GREENGUARD Gold, BIFMA'\n        },\n        featured: true\n    }\n];\n\nconst mockCategories = [\n    { id: 1, name: 'Office Desks', description: 'Professional office desks and workstations' },\n    { id: 2, name: 'Office Chairs', description: 'Ergonomic and executive office chairs' },\n    { id: 3, name: 'Storage', description: 'Filing cabinets, bookcases, and storage units' },\n    { id: 4, name: 'Conference', description: 'Conference tables and meeting room furniture' },\n    { id: 5, name: 'Accessories', description: 'Office accessories and organizational tools' }\n];\n\n// Helper function to simulate API delay\nconst delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));\n\nexport const productService = {\n    async getAllProducts(params = {}) {\n        // Using mock data only (no backend)\n        await delay(500);\n\n        const { page = 1, limit = 10, category, search } = params;\n        let filteredProducts = [...mockProducts];\n\n        // Apply filters\n        if (category) {\n            filteredProducts = filteredProducts.filter(p =>\n                p.categoryName.toLowerCase().includes(category.toLowerCase())\n            );\n        }\n\n        if (search) {\n            filteredProducts = filteredProducts.filter(p =>\n                p.name.toLowerCase().includes(search.toLowerCase()) ||\n                p.description.toLowerCase().includes(search.toLowerCase())\n            );\n        }\n\n        // Apply pagination\n        const startIndex = (page - 1) * limit;\n        const endIndex = startIndex + limit;\n        const paginatedProducts = filteredProducts.slice(startIndex, endIndex);\n\n        return {\n            products: paginatedProducts,\n            total: filteredProducts.length,\n            page: parseInt(page),\n            totalPages: Math.ceil(filteredProducts.length / limit)\n        };\n    },\n\n    async getProductById(id) {\n        // Using mock data only (no backend)\n        await delay(300);\n\n        const product = mockProducts.find(p => p.id === parseInt(id));\n        if (!product) {\n            throw new Error('Product not found');\n        }\n\n        return { product };\n    },\n\n    async getFeaturedProducts(limit = 8) {\n        // Using mock data only (no backend)\n        await delay(400);\n\n        const featuredProducts = mockProducts.filter(p => p.featured);\n        return { products: featuredProducts };\n    },\n\n    async getCategories() {\n        // Using mock data only (no backend)\n        await delay(200);\n\n        return { categories: mockCategories };\n    }\n};\n\n// Export individual functions for easier imports\nexport const getAllProducts = productService.getAllProducts;\nexport const getProductById = productService.getProductById;\nexport const getFeaturedProducts = productService.getFeaturedProducts;\nexport const getCategories = productService.getCategories;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;;AAEvB;AACA,MAAMC,YAAY,GAAG,CACjB;EACIC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,yBAAyB;EAC/BC,WAAW,EAAE,+KAA+K;EAC5LC,KAAK,EAAE,OAAO;EACdC,aAAa,EAAE,OAAO;EACtBC,YAAY,EAAE,cAAc;EAC5BC,MAAM,EAAE,CACJ,oEAAoE,EACpE,oEAAoE,CACvE;EACDC,OAAO,EAAE,4BAA4B;EACrCC,cAAc,EAAE;IACZC,QAAQ,EAAE,gBAAgB;IAC1BC,UAAU,EAAE,iBAAiB;IAC7BC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,qBAAqB;IAC7BC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;AACd,CAAC,EACD;EACIf,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,6BAA6B;EACnCC,WAAW,EAAE,sHAAsH;EACnIC,KAAK,EAAE,OAAO;EACdC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,cAAc;EAC5BC,MAAM,EAAE,CACJ,iEAAiE,EACjE,oEAAoE,CACvE;EACDC,OAAO,EAAE,wBAAwB;EACjCC,cAAc,EAAE;IACZC,QAAQ,EAAE,yBAAyB;IACnCC,UAAU,EAAE,iBAAiB;IAC7BC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,iBAAiB;IACzBI,OAAO,EAAE,WAAW;IACpBF,QAAQ,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;AACd,CAAC,EACD;EACIf,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,2BAA2B;EACjCC,WAAW,EAAE,4GAA4G;EACzHC,KAAK,EAAE,MAAM;EACbC,aAAa,EAAE,MAAM;EACrBC,YAAY,EAAE,eAAe;EAC7BC,MAAM,EAAE,CACJ,oEAAoE,EACpE,oEAAoE,CACvE;EACDC,OAAO,EAAE,6BAA6B;EACtCC,cAAc,EAAE;IACZC,QAAQ,EAAE,iBAAiB;IAC3BQ,eAAe,EAAE,SAAS;IAC1BC,WAAW,EAAE,wBAAwB;IACrCJ,QAAQ,EAAE,SAAS;IACnBK,aAAa,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU;EAChD,CAAC;EACDJ,QAAQ,EAAE;AACd,CAAC,EACD;EACIf,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,yBAAyB;EAC/BC,WAAW,EAAE,6HAA6H;EAC1IC,KAAK,EAAE,MAAM;EACbC,aAAa,EAAE,MAAM;EACrBC,YAAY,EAAE,cAAc;EAC5BC,MAAM,EAAE,CACJ,iEAAiE,CACpE;EACDC,OAAO,EAAE,2BAA2B;EACpCC,cAAc,EAAE;IACZC,QAAQ,EAAE,cAAc;IACxBC,UAAU,EAAE,iBAAiB;IAC7BC,MAAM,EAAE,QAAQ;IAChBS,YAAY,EAAE,aAAa;IAC3BN,QAAQ,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;AACd,CAAC,EACD;EACIf,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,0BAA0B;EAChCC,WAAW,EAAE,+GAA+G;EAC5HC,KAAK,EAAE,MAAM;EACbC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,mBAAmB;EACjCC,MAAM,EAAE,CACJ,oEAAoE,CACvE;EACDC,OAAO,EAAE,4BAA4B;EACrCC,cAAc,EAAE;IACZC,QAAQ,EAAE,YAAY;IACtBC,UAAU,EAAE,iBAAiB;IAC7BG,OAAO,EAAE,CAAC;IACVQ,IAAI,EAAE,wBAAwB;IAC9BP,QAAQ,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;AACd,CAAC,EACD;EACIf,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,uBAAuB;EAC7BC,WAAW,EAAE,2GAA2G;EACxHC,KAAK,EAAE,MAAM;EACbC,aAAa,EAAE,MAAM;EACrBC,YAAY,EAAE,mBAAmB;EACjCC,MAAM,EAAE,CACJ,oEAAoE,CACvE;EACDC,OAAO,EAAE,uBAAuB;EAChCC,cAAc,EAAE;IACZC,QAAQ,EAAE,iBAAiB;IAC3BC,UAAU,EAAE,iBAAiB;IAC7BY,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE,IAAI;IAChBT,QAAQ,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;AACd,CAAC,EACD;EACIf,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE,yGAAyG;EACtHC,KAAK,EAAE,MAAM;EACbC,aAAa,EAAE,MAAM;EACrBC,YAAY,EAAE,eAAe;EAC7BC,MAAM,EAAE,CACJ,oEAAoE,CACvE;EACDC,OAAO,EAAE,wBAAwB;EACjCC,cAAc,EAAE;IACZC,QAAQ,EAAE,gBAAgB;IAC1BQ,eAAe,EAAE,SAAS;IAC1BC,WAAW,EAAE,cAAc;IAC3BJ,QAAQ,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;AACd,CAAC,EACD;EACIf,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,kBAAkB;EACxBC,WAAW,EAAE,6FAA6F;EAC1GC,KAAK,EAAE,OAAO;EACdC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,YAAY;EAC1BC,MAAM,EAAE,CACJ,iEAAiE,CACpE;EACDC,OAAO,EAAE,8BAA8B;EACvCC,cAAc,EAAE;IACZC,QAAQ,EAAE,WAAW;IACrBC,UAAU,EAAE,iBAAiB;IAC7Bc,OAAO,EAAE,aAAa;IACtBV,QAAQ,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;AACd,CAAC,EACD;EACIf,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,oBAAoB;EAC1BC,WAAW,EAAE,kFAAkF;EAC/FC,KAAK,EAAE,KAAK;EACZC,aAAa,EAAE,KAAK;EACpBC,YAAY,EAAE,aAAa;EAC3BC,MAAM,EAAE,CACJ,oEAAoE,CACvE;EACDC,OAAO,EAAE,4BAA4B;EACrCC,cAAc,EAAE;IACZC,QAAQ,EAAE,QAAQ;IAClBgB,MAAM,EAAE,CAAC;IACTX,QAAQ,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;AACd,CAAC,EACD;EACIf,EAAE,EAAE,EAAE;EACNC,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EAAE,gGAAgG;EAC7GC,KAAK,EAAE,MAAM;EACbC,aAAa,EAAE,MAAM;EACrBC,YAAY,EAAE,cAAc;EAC5BC,MAAM,EAAE,CACJ,iEAAiE,CACpE;EACDC,OAAO,EAAE,2BAA2B;EACpCC,cAAc,EAAE;IACZC,QAAQ,EAAE,iBAAiB;IAC3BC,UAAU,EAAE,iBAAiB;IAC7BY,OAAO,EAAE,CAAC;IACVR,QAAQ,EAAE;EACd,CAAC;EACDC,QAAQ,EAAE;AACd,CAAC,EACD;EACIf,EAAE,EAAE,EAAE;EACNC,IAAI,EAAE,wBAAwB;EAC9BC,WAAW,EAAE,yPAAyP;EACtQC,KAAK,EAAE,MAAM;EACbC,aAAa,EAAE,MAAM;EACrBC,YAAY,EAAE,eAAe;EAC7BC,MAAM,EAAE,CACJ,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,CACvE;EACDC,OAAO,EAAE,yBAAyB;EAClCC,cAAc,EAAE;IACZC,QAAQ,EAAE,0BAA0B;IACpCQ,eAAe,EAAE,SAAS;IAC1BS,WAAW,EAAE,cAAc;IAC3BC,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,WAAW;IACvBC,WAAW,EAAE,WAAW;IACxBX,WAAW,EAAE,sCAAsC;IACnDY,QAAQ,EAAE,uBAAuB;IACjCC,IAAI,EAAE,2CAA2C;IACjDjB,QAAQ,EAAE,SAAS;IACnBK,aAAa,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;IAC/Ca,cAAc,EAAE;EACpB,CAAC;EACDjB,QAAQ,EAAE;AACd,CAAC,CACJ;AAED,MAAMkB,cAAc,GAAG,CACnB;EAAEjC,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,cAAc;EAAEC,WAAW,EAAE;AAA6C,CAAC,EAC1F;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,eAAe;EAAEC,WAAW,EAAE;AAAwC,CAAC,EACtF;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,SAAS;EAAEC,WAAW,EAAE;AAAgD,CAAC,EACxF;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,YAAY;EAAEC,WAAW,EAAE;AAA+C,CAAC,EAC1F;EAAEF,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,aAAa;EAAEC,WAAW,EAAE;AAA8C,CAAC,CAC7F;;AAED;AACA,MAAMgC,KAAK,GAAIC,EAAE,IAAK,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC,CAAC;AAErE,OAAO,MAAMI,cAAc,GAAG;EAC1B,MAAMC,cAAcA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC9B;IACA,MAAMP,KAAK,CAAC,GAAG,CAAC;IAEhB,MAAM;MAAEQ,IAAI,GAAG,CAAC;MAAEC,KAAK,GAAG,EAAE;MAAEC,QAAQ;MAAEC;IAAO,CAAC,GAAGJ,MAAM;IACzD,IAAIK,gBAAgB,GAAG,CAAC,GAAG/C,YAAY,CAAC;;IAExC;IACA,IAAI6C,QAAQ,EAAE;MACVE,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CAACC,CAAC,IACxCA,CAAC,CAAC3C,YAAY,CAAC4C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACN,QAAQ,CAACK,WAAW,CAAC,CAAC,CAChE,CAAC;IACL;IAEA,IAAIJ,MAAM,EAAE;MACRC,gBAAgB,GAAGA,gBAAgB,CAACC,MAAM,CAACC,CAAC,IACxCA,CAAC,CAAC/C,IAAI,CAACgD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,MAAM,CAACI,WAAW,CAAC,CAAC,CAAC,IACnDD,CAAC,CAAC9C,WAAW,CAAC+C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,MAAM,CAACI,WAAW,CAAC,CAAC,CAC7D,CAAC;IACL;;IAEA;IACA,MAAME,UAAU,GAAG,CAACT,IAAI,GAAG,CAAC,IAAIC,KAAK;IACrC,MAAMS,QAAQ,GAAGD,UAAU,GAAGR,KAAK;IACnC,MAAMU,iBAAiB,GAAGP,gBAAgB,CAACQ,KAAK,CAACH,UAAU,EAAEC,QAAQ,CAAC;IAEtE,OAAO;MACHG,QAAQ,EAAEF,iBAAiB;MAC3BG,KAAK,EAAEV,gBAAgB,CAACW,MAAM;MAC9Bf,IAAI,EAAEgB,QAAQ,CAAChB,IAAI,CAAC;MACpBiB,UAAU,EAAEC,IAAI,CAACC,IAAI,CAACf,gBAAgB,CAACW,MAAM,GAAGd,KAAK;IACzD,CAAC;EACL,CAAC;EAED,MAAMmB,cAAcA,CAAC9D,EAAE,EAAE;IACrB;IACA,MAAMkC,KAAK,CAAC,GAAG,CAAC;IAEhB,MAAM6B,OAAO,GAAGhE,YAAY,CAACiE,IAAI,CAAChB,CAAC,IAAIA,CAAC,CAAChD,EAAE,KAAK0D,QAAQ,CAAC1D,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC+D,OAAO,EAAE;MACV,MAAM,IAAIE,KAAK,CAAC,mBAAmB,CAAC;IACxC;IAEA,OAAO;MAAEF;IAAQ,CAAC;EACtB,CAAC;EAED,MAAMG,mBAAmBA,CAACvB,KAAK,GAAG,CAAC,EAAE;IACjC;IACA,MAAMT,KAAK,CAAC,GAAG,CAAC;IAEhB,MAAMiC,gBAAgB,GAAGpE,YAAY,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjC,QAAQ,CAAC;IAC7D,OAAO;MAAEwC,QAAQ,EAAEY;IAAiB,CAAC;EACzC,CAAC;EAED,MAAMC,aAAaA,CAAA,EAAG;IAClB;IACA,MAAMlC,KAAK,CAAC,GAAG,CAAC;IAEhB,OAAO;MAAEmC,UAAU,EAAEpC;IAAe,CAAC;EACzC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMO,cAAc,GAAGD,cAAc,CAACC,cAAc;AAC3D,OAAO,MAAMsB,cAAc,GAAGvB,cAAc,CAACuB,cAAc;AAC3D,OAAO,MAAMI,mBAAmB,GAAG3B,cAAc,CAAC2B,mBAAmB;AACrE,OAAO,MAAME,aAAa,GAAG7B,cAAc,CAAC6B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}