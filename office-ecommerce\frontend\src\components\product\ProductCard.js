import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';
import CheckoutModal from '../cart/CheckoutModal';

const ProductCard = ({ product }) => {
    const { addToCart } = useCart();
    const [showCheckoutModal, setShowCheckoutModal] = useState(false);
    const [quickConfig, setQuickConfig] = useState({
        color: 'default',
        material: 'default',
        size: 'standard'
    });
    const {
        id,
        name,
        price,
        discountPrice,
        images,
        categoryName,
        featured
    } = product;

    // Calculate price based on quick configuration
    const calculateConfiguredPrice = (basePrice, config) => {
        let multiplier = 1;

        // Color price modifiers
        if (config.color === 'cherry') multiplier += 0.15;
        else if (config.color === 'dark-walnut') multiplier += 0.10;
        else if (config.color === 'black' || config.color === 'white') multiplier += 0.05;

        // Material price modifiers
        if (config.material === 'solid-wood') multiplier += 0.25;
        else if (config.material === 'metal') multiplier += 0.20;
        else if (config.material === 'glass') multiplier += 0.15;

        // Size price modifiers
        if (config.size === 'large') multiplier += 0.20;
        else if (config.size === 'xl') multiplier += 0.35;
        else if (config.size === 'compact') multiplier -= 0.10;

        return basePrice * multiplier;
    };

    const baseDisplayPrice = discountPrice || price;
    const configuredPrice = calculateConfiguredPrice(baseDisplayPrice, quickConfig);
    const hasDiscount = discountPrice && discountPrice < price;
    const hasConfiguration = quickConfig.color !== 'default' || quickConfig.material !== 'default' || quickConfig.size !== 'standard';
    const primaryImage = images && images.length > 0 ? images[0] : '/placeholder-image.jpg';

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };

    const handleAddToCart = (e) => {
        e.preventDefault(); // Prevent navigation when clicking add to cart
        try {
            // Create product with quick configuration options
            const configuredProduct = {
                ...product,
                quickConfiguration: quickConfig,
                configuredPrice: calculateConfiguredPrice(product.price, quickConfig)
            };
            addToCart(configuredProduct, 1); // Add 1 item with quick config settings
            setShowCheckoutModal(true); // Show checkout modal
        } catch (error) {
            console.error('Error adding to cart:', error);
            alert('Failed to add item to cart. Please try again.');
        }
    };

    return (
        <div className="product-card">
            {featured && <div className="featured-badge">Featured</div>}
            {hasDiscount && (
                <div className="discount-badge">
                    {Math.round(((price - discountPrice) / price) * 100)}% OFF
                </div>
            )}

            <Link to={`/product/${id}`} className="product-link">
                <div className="product-image">
                    <img
                        src={primaryImage}
                        alt={name}
                        onError={(e) => {
                            e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop';
                        }}
                    />
                </div>

                <div className="product-info">
                    <div className="product-category">{categoryName}</div>
                    <h3 className="product-name">{name}</h3>

                    <div className="product-pricing">
                        <span className="current-price">
                            {formatPrice(hasConfiguration ? configuredPrice : baseDisplayPrice)}
                        </span>
                        {hasConfiguration && (
                            <span className="base-price">{formatPrice(baseDisplayPrice)}</span>
                        )}
                        {hasDiscount && !hasConfiguration && (
                            <span className="original-price">{formatPrice(price)}</span>
                        )}
                        {hasConfiguration && (
                            <span className="config-indicator">Configured</span>
                        )}
                    </div>
                </div>
            </Link>

            <div className="product-actions">
                <Link to={`/product/${id}`} className="btn btn-primary btn-compact">
                    View Details
                </Link>
                <button
                    className="btn btn-secondary btn-compact"
                    onClick={handleAddToCart}
                >
                    Add to Cart
                </button>
            </div>

            {/* 3D Configuration Quick Actions */}
            <div className="config-actions">
                <button
                    className={`config-btn color-btn ${quickConfig.color !== 'default' ? 'active' : ''}`}
                    onClick={(e) => {
                        e.preventDefault();
                        const colors = ['default', 'dark-walnut', 'cherry', 'black', 'white'];
                        const currentIndex = colors.indexOf(quickConfig.color);
                        const nextColor = colors[(currentIndex + 1) % colors.length];
                        setQuickConfig(prev => ({ ...prev, color: nextColor }));
                    }}
                    title={`Color: ${quickConfig.color.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}`}
                >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                        <path d="M12 2a10 10 0 0 0 0 20 10 10 0 0 1 0-20z" fill="currentColor"/>
                    </svg>
                </button>

                <button
                    className={`config-btn material-btn ${quickConfig.material !== 'default' ? 'active' : ''}`}
                    onClick={(e) => {
                        e.preventDefault();
                        const materials = ['default', 'solid-wood', 'engineered-wood', 'metal', 'glass'];
                        const currentIndex = materials.indexOf(quickConfig.material);
                        const nextMaterial = materials[(currentIndex + 1) % materials.length];
                        setQuickConfig(prev => ({ ...prev, material: nextMaterial }));
                    }}
                    title={`Material: ${quickConfig.material.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}`}
                >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2"/>
                        <path d="M9 9h6v6H9z" fill="currentColor"/>
                    </svg>
                </button>

                <button
                    className={`config-btn size-btn ${quickConfig.size !== 'standard' ? 'active' : ''}`}
                    onClick={(e) => {
                        e.preventDefault();
                        const sizes = ['standard', 'compact', 'large', 'xl'];
                        const currentIndex = sizes.indexOf(quickConfig.size);
                        const nextSize = sizes[(currentIndex + 1) % sizes.length];
                        setQuickConfig(prev => ({ ...prev, size: nextSize }));
                    }}
                    title={`Size: ${quickConfig.size.toUpperCase()}`}
                >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </button>

                <Link
                    to={`/product/${id}?configurator=true`}
                    className="config-btn full-config-btn"
                    title="Full 3D Configurator"
                >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="currentColor"/>
                        <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </Link>
            </div>

            {/* Checkout Modal */}
            <CheckoutModal
                isOpen={showCheckoutModal}
                onClose={() => setShowCheckoutModal(false)}
                product={product}
                quantity={1}
            />
        </div>
    );
};

export default ProductCard;
