{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\About.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"about-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"breadcrumb\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 21\n        }, this), \" /\", /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"About\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"our-story-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"story-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Our Story\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"For over two decades, we've been crafting exceptional office environments that inspire creativity, enhance productivity, and elevate the modern workplace experience.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"story-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"story-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Crafting Excellence Since 2000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"What began as a small workshop has grown into a leading provider of premium office furniture solutions. Our commitment to quality, innovation and customer satisfaction has been our guiding principle.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stats-grid\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"2000+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Projects Completed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"500+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 38,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Happy Clients\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"story-right\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"story-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop\",\n                alt: \"Modern office workspace\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"our-mission-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mission-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mission-left\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mission-image\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop\",\n                alt: \"Office design consultation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mission-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Our Mission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"We strive to transform workspaces into inspiring environments where creativity flourishes and productivity soars. Through innovative design and unwavering attention to detail, we create furniture solutions that perfectly balance form and function.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mission-features\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-icon\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Premium Materials\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-icon\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Ergonomic Design\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-icon\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Sustainable Practices\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"our-values-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"values-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Our Values\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"values-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon innovation\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z\",\n                  fill: \"#F0B21B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Innovation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Pushing boundaries in design and functionality\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon quality\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"3\",\n                  width: \"18\",\n                  height: \"18\",\n                  rx: \"2\",\n                  fill: \"#F0B21B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 12L11 14L15 10\",\n                  stroke: \"white\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Quality\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Uncompromising commitment to excellence\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon service\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"12\",\n                  cy: \"12\",\n                  r: \"10\",\n                  fill: \"#F0B21B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M8 14S9.5 16 12 16S16 14 16 14\",\n                  stroke: \"white\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"9\",\n                  cy: \"9\",\n                  r: \"1\",\n                  fill: \"white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"15\",\n                  cy: \"9\",\n                  r: \"1\",\n                  fill: \"white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Dedicated to exceeding expectations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"value-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value-icon sustainability\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"24\",\n                height: \"24\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L6.83 7.17L2 12L3.41 13.41L6.83 10L10.5 13.67L16.17 8L18.83 10.67L17 12.5L19 14.5L21 9Z\",\n                  fill: \"#F0B21B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Sustainability\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Committed to environmental responsibility\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"design-philosophy-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"philosophy-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Design Philosophy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Every element of our brand reflects our commitment to clean, modern design\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"philosophy-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"philosophy-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Typography & Visual Identity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Our design language embraces simplicity and clarity. We use clean, modern typography that enhances readability and creates a professional, approachable aesthetic. Every font choice is carefully considered to reflect our values of innovation and quality.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"typography-features\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"typo-feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Clean & Modern\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Sans-serif fonts that provide excellent readability across all devices\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"typo-feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Consistent Hierarchy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Well-defined text sizes and weights that guide user attention\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"typo-feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Accessible Design\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"High contrast ratios and legible font sizes for all users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"philosophy-right\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-showcase\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-example\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"font-display\",\n                  children: \"Office Excellence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-label\",\n                  children: \"Primary Heading - Bold, Impactful\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-example\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-subtitle\",\n                  children: \"Modern Workspace Solutions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-label\",\n                  children: \"Subtitle - Clean, Professional\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-example\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-body\",\n                  children: \"Our carefully selected typography creates a harmonious reading experience that reflects our commitment to quality and attention to detail.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-label\",\n                  children: \"Body Text - Readable, Elegant\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 9\n  }, this);\n};\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "About", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "width", "height", "viewBox", "fill", "xmlns", "d", "x", "y", "rx", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "cx", "cy", "r", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/About.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst About = () => {\n    return (\n        <div className=\"about-page\">\n            <div className=\"container\">\n                <div className=\"breadcrumb\">\n                    <Link to=\"/\">Home</Link> /\n                    <span>About</span>\n                </div>\n\n                {/* Our Story Section */}\n                <section className=\"our-story-section\">\n                    <div className=\"story-header\">\n                        <h1>Our Story</h1>\n                        <p>\n                            For over two decades, we've been crafting exceptional office environments that inspire\n                            creativity, enhance productivity, and elevate the modern workplace experience.\n                        </p>\n                    </div>\n\n                    <div className=\"story-content\">\n                        <div className=\"story-left\">\n                            <h2>Crafting Excellence Since 2000</h2>\n                            <p>\n                                What began as a small workshop has grown into a leading provider of\n                                premium office furniture solutions. Our commitment to quality, innovation\n                                and customer satisfaction has been our guiding principle.\n                            </p>\n\n                            <div className=\"stats-grid\">\n                                <div className=\"stat-item\">\n                                    <h3>2000+</h3>\n                                    <p>Projects Completed</p>\n                                </div>\n                                <div className=\"stat-item\">\n                                    <h3>500+</h3>\n                                    <p>Happy Clients</p>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div className=\"story-right\">\n                            <div className=\"story-image\">\n                                <img\n                                    src=\"https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop\"\n                                    alt=\"Modern office workspace\"\n                                />\n                            </div>\n                        </div>\n                    </div>\n                </section>\n\n                {/* Our Mission Section */}\n                <section className=\"our-mission-section\">\n                    <div className=\"mission-content\">\n                        <div className=\"mission-left\">\n                            <div className=\"mission-image\">\n                                <img\n                                    src=\"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop\"\n                                    alt=\"Office design consultation\"\n                                />\n                            </div>\n                        </div>\n\n                        <div className=\"mission-right\">\n                            <h2>Our Mission</h2>\n                            <p>\n                                We strive to transform workspaces into inspiring environments where\n                                creativity flourishes and productivity soars. Through innovative design and\n                                unwavering attention to detail, we create furniture solutions that perfectly\n                                balance form and function.\n                            </p>\n\n                            <div className=\"mission-features\">\n                                <div className=\"feature-item\">\n                                    <div className=\"feature-icon\">✓</div>\n                                    <span>Premium Materials</span>\n                                </div>\n                                <div className=\"feature-item\">\n                                    <div className=\"feature-icon\">✓</div>\n                                    <span>Ergonomic Design</span>\n                                </div>\n                                <div className=\"feature-item\">\n                                    <div className=\"feature-icon\">✓</div>\n                                    <span>Sustainable Practices</span>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </section>\n\n                {/* Our Values Section */}\n                <section className=\"our-values-section\">\n                    <div className=\"values-header\">\n                        <h2>Our Values</h2>\n                    </div>\n\n                    <div className=\"values-grid\">\n                        <div className=\"value-card\">\n                            <div className=\"value-icon innovation\">\n                                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                    <path d=\"M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z\" fill=\"#F0B21B\"/>\n                                </svg>\n                            </div>\n                            <h3>Innovation</h3>\n                            <p>Pushing boundaries in design and functionality</p>\n                        </div>\n\n                        <div className=\"value-card\">\n                            <div className=\"value-icon quality\">\n                                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                    <rect x=\"3\" y=\"3\" width=\"18\" height=\"18\" rx=\"2\" fill=\"#F0B21B\"/>\n                                    <path d=\"M9 12L11 14L15 10\" stroke=\"white\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                </svg>\n                            </div>\n                            <h3>Quality</h3>\n                            <p>Uncompromising commitment to excellence</p>\n                        </div>\n\n                        <div className=\"value-card\">\n                            <div className=\"value-icon service\">\n                                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                    <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#F0B21B\"/>\n                                    <path d=\"M8 14S9.5 16 12 16S16 14 16 14\" stroke=\"white\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                                    <circle cx=\"9\" cy=\"9\" r=\"1\" fill=\"white\"/>\n                                    <circle cx=\"15\" cy=\"9\" r=\"1\" fill=\"white\"/>\n                                </svg>\n                            </div>\n                            <h3>Service</h3>\n                            <p>Dedicated to exceeding expectations</p>\n                        </div>\n\n                        <div className=\"value-card\">\n                            <div className=\"value-icon sustainability\">\n                                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                                    <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L6.83 7.17L2 12L3.41 13.41L6.83 10L10.5 13.67L16.17 8L18.83 10.67L17 12.5L19 14.5L21 9Z\" fill=\"#F0B21B\"/>\n                                </svg>\n                            </div>\n                            <h3>Sustainability</h3>\n                            <p>Committed to environmental responsibility</p>\n                        </div>\n                    </div>\n                </section>\n\n                {/* Design Philosophy Section */}\n                <section className=\"design-philosophy-section\">\n                    <div className=\"philosophy-header\">\n                        <h2>Design Philosophy</h2>\n                        <p>Every element of our brand reflects our commitment to clean, modern design</p>\n                    </div>\n\n                    <div className=\"philosophy-content\">\n                        <div className=\"philosophy-left\">\n                            <h3>Typography & Visual Identity</h3>\n                            <p>\n                                Our design language embraces simplicity and clarity. We use clean, modern typography\n                                that enhances readability and creates a professional, approachable aesthetic.\n                                Every font choice is carefully considered to reflect our values of innovation and quality.\n                            </p>\n\n                            <div className=\"typography-features\">\n                                <div className=\"typo-feature\">\n                                    <h4>Clean & Modern</h4>\n                                    <p>Sans-serif fonts that provide excellent readability across all devices</p>\n                                </div>\n                                <div className=\"typo-feature\">\n                                    <h4>Consistent Hierarchy</h4>\n                                    <p>Well-defined text sizes and weights that guide user attention</p>\n                                </div>\n                                <div className=\"typo-feature\">\n                                    <h4>Accessible Design</h4>\n                                    <p>High contrast ratios and legible font sizes for all users</p>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div className=\"philosophy-right\">\n                            <div className=\"font-showcase\">\n                                <div className=\"font-example\">\n                                    <h1 className=\"font-display\">Office Excellence</h1>\n                                    <p className=\"font-label\">Primary Heading - Bold, Impactful</p>\n                                </div>\n                                <div className=\"font-example\">\n                                    <h3 className=\"font-subtitle\">Modern Workspace Solutions</h3>\n                                    <p className=\"font-label\">Subtitle - Clean, Professional</p>\n                                </div>\n                                <div className=\"font-example\">\n                                    <p className=\"font-body\">\n                                        Our carefully selected typography creates a harmonious reading experience\n                                        that reflects our commitment to quality and attention to detail.\n                                    </p>\n                                    <p className=\"font-label\">Body Text - Readable, Elegant</p>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </section>\n\n            </div>\n        </div>\n    );\n};\n\nexport default About;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAChB,oBACID,OAAA;IAAKE,SAAS,EAAC,YAAY;IAAAC,QAAA,eACvBH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBH,OAAA;QAAKE,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACvBH,OAAA,CAACF,IAAI;UAACM,EAAE,EAAC,GAAG;UAAAD,QAAA,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,MACxB,eAAAR,OAAA;UAAAG,QAAA,EAAM;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAGNR,OAAA;QAASE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAClCH,OAAA;UAAKE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBH,OAAA;YAAAG,QAAA,EAAI;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClBR,OAAA;YAAAG,QAAA,EAAG;UAGH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENR,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BH,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBH,OAAA;cAAAG,QAAA,EAAI;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCR,OAAA;cAAAG,QAAA,EAAG;YAIH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJR,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACvBH,OAAA;gBAAKE,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtBH,OAAA;kBAAAG,QAAA,EAAI;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACdR,OAAA;kBAAAG,QAAA,EAAG;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACNR,OAAA;gBAAKE,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACtBH,OAAA;kBAAAG,QAAA,EAAI;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACbR,OAAA;kBAAAG,QAAA,EAAG;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENR,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,eACxBH,OAAA;cAAKE,SAAS,EAAC,aAAa;cAAAC,QAAA,eACxBH,OAAA;gBACIS,GAAG,EAAC,mFAAmF;gBACvFC,GAAG,EAAC;cAAyB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGVR,OAAA;QAASE,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eACpCH,OAAA;UAAKE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BH,OAAA;YAAKE,SAAS,EAAC,cAAc;YAAAC,QAAA,eACzBH,OAAA;cAAKE,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC1BH,OAAA;gBACIS,GAAG,EAAC,gFAAgF;gBACpFC,GAAG,EAAC;cAA4B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENR,OAAA;YAAKE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BH,OAAA;cAAAG,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBR,OAAA;cAAAG,QAAA,EAAG;YAKH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJR,OAAA;cAAKE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC7BH,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBH,OAAA;kBAAKE,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCR,OAAA;kBAAAG,QAAA,EAAM;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACNR,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBH,OAAA;kBAAKE,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCR,OAAA;kBAAAG,QAAA,EAAM;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACNR,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBH,OAAA;kBAAKE,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCR,OAAA;kBAAAG,QAAA,EAAM;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGVR,OAAA;QAASE,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACnCH,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC1BH,OAAA;YAAAG,QAAA,EAAI;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eAENR,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBH,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBH,OAAA;cAAKE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eAClCH,OAAA;gBAAKW,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,4BAA4B;gBAAAZ,QAAA,eAC1FH,OAAA;kBAAMgB,CAAC,EAAC,wEAAwE;kBAACF,IAAI,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNR,OAAA;cAAAG,QAAA,EAAI;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBR,OAAA;cAAAG,QAAA,EAAG;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eAENR,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBH,OAAA;cAAKE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eAC/BH,OAAA;gBAAKW,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,4BAA4B;gBAAAZ,QAAA,gBAC1FH,OAAA;kBAAMiB,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACP,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACO,EAAE,EAAC,GAAG;kBAACL,IAAI,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChER,OAAA;kBAAMgB,CAAC,EAAC,mBAAmB;kBAACI,MAAM,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNR,OAAA;cAAAG,QAAA,EAAI;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBR,OAAA;cAAAG,QAAA,EAAG;YAAuC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAENR,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBH,OAAA;cAAKE,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eAC/BH,OAAA;gBAAKW,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,4BAA4B;gBAAAZ,QAAA,gBAC1FH,OAAA;kBAAQwB,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACZ,IAAI,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/CR,OAAA;kBAAMgB,CAAC,EAAC,gCAAgC;kBAACI,MAAM,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/FR,OAAA;kBAAQwB,EAAE,EAAC,GAAG;kBAACC,EAAE,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACZ,IAAI,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC1CR,OAAA;kBAAQwB,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACZ,IAAI,EAAC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNR,OAAA;cAAAG,QAAA,EAAI;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBR,OAAA;cAAAG,QAAA,EAAG;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eAENR,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBH,OAAA;cAAKE,SAAS,EAAC,2BAA2B;cAAAC,QAAA,eACtCH,OAAA;gBAAKW,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,4BAA4B;gBAAAZ,QAAA,eAC1FH,OAAA;kBAAMgB,CAAC,EAAC,sNAAsN;kBAACF,IAAI,EAAC;gBAAS;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNR,OAAA;cAAAG,QAAA,EAAI;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBR,OAAA;cAAAG,QAAA,EAAG;YAAyC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGVR,OAAA;QAASE,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAC1CH,OAAA;UAAKE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAC9BH,OAAA;YAAAG,QAAA,EAAI;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BR,OAAA;YAAAG,QAAA,EAAG;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eAENR,OAAA;UAAKE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAC/BH,OAAA;YAAKE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BH,OAAA;cAAAG,QAAA,EAAI;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrCR,OAAA;cAAAG,QAAA,EAAG;YAIH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJR,OAAA;cAAKE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAChCH,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBH,OAAA;kBAAAG,QAAA,EAAI;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvBR,OAAA;kBAAAG,QAAA,EAAG;gBAAsE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACNR,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBH,OAAA;kBAAAG,QAAA,EAAI;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BR,OAAA;kBAAAG,QAAA,EAAG;gBAA6D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNR,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBH,OAAA;kBAAAG,QAAA,EAAI;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BR,OAAA;kBAAAG,QAAA,EAAG;gBAAyD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENR,OAAA;YAAKE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7BH,OAAA;cAAKE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1BH,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBH,OAAA;kBAAIE,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnDR,OAAA;kBAAGE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNR,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBH,OAAA;kBAAIE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7DR,OAAA;kBAAGE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACNR,OAAA;gBAAKE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBH,OAAA;kBAAGE,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAGzB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJR,OAAA;kBAAGE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAA6B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAET;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACmB,EAAA,GAxMI1B,KAAK;AA0MX,eAAeA,KAAK;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}