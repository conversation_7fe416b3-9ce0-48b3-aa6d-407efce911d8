USE OfficeEcommerce;
GO

-- Create SalesAnalytics table for sales reporting
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SalesAnalytics' AND xtype='U')
BEGIN
    CREATE TABLE SalesAnalytics (
        id INT PRIMARY KEY IDENTITY(1,1),
        date DATE NOT NULL,
        productId INT NULL,
        categoryId INT NULL,
        totalOrders INT DEFAULT 0,
        totalQuantitySold INT DEFAULT 0,
        totalRevenue DECIMAL(12,2) DEFAULT 0,
        totalCost DECIMAL(12,2) DEFAULT 0,
        totalProfit AS (totalRevenue - totalCost) PERSISTED,
        averageOrderValue DECIMAL(10,2) DEFAULT 0,
        uniqueCustomers INT DEFAULT 0,
        returnsCount INT DEFAULT 0,
        returnsValue DECIMAL(12,2) DEFAULT 0,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_SalesAnalytics_Products FOREIGN KEY (productId) REFERENCES Products(id),
        CONSTRAINT FK_SalesAnalytics_Categories FOREIGN KEY (categoryId) REFERENCES Categories(id),
        CONSTRAINT UQ_SalesAnalytics_DateProduct UNIQUE (date, productId, categoryId)
    );

    -- Create indexes
    CREATE INDEX IX_SalesAnalytics_Date ON SalesAnalytics(date);
    CREATE INDEX IX_SalesAnalytics_ProductId ON SalesAnalytics(productId);
    CREATE INDEX IX_SalesAnalytics_CategoryId ON SalesAnalytics(categoryId);
    CREATE INDEX IX_SalesAnalytics_TotalRevenue ON SalesAnalytics(totalRevenue);

    PRINT 'SalesAnalytics table created successfully';
END
ELSE
BEGIN
    PRINT 'SalesAnalytics table already exists';
END
GO

-- Create InventoryAnalytics table for inventory reporting
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='InventoryAnalytics' AND xtype='U')
BEGIN
    CREATE TABLE InventoryAnalytics (
        id INT PRIMARY KEY IDENTITY(1,1),
        date DATE NOT NULL,
        productId INT NULL,
        categoryId INT NULL,
        totalStock INT DEFAULT 0,
        stockValue DECIMAL(12,2) DEFAULT 0,
        lowStockItems INT DEFAULT 0,
        outOfStockItems INT DEFAULT 0,
        stockTurnoverRate DECIMAL(8,4) DEFAULT 0,
        daysOfInventory DECIMAL(8,2) DEFAULT 0,
        deadStockValue DECIMAL(12,2) DEFAULT 0, -- Items not sold in 90+ days
        fastMovingItems INT DEFAULT 0,
        slowMovingItems INT DEFAULT 0,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_InventoryAnalytics_Products FOREIGN KEY (productId) REFERENCES Products(id),
        CONSTRAINT FK_InventoryAnalytics_Categories FOREIGN KEY (categoryId) REFERENCES Categories(id),
        CONSTRAINT UQ_InventoryAnalytics_DateProduct UNIQUE (date, productId, categoryId)
    );

    -- Create indexes
    CREATE INDEX IX_InventoryAnalytics_Date ON InventoryAnalytics(date);
    CREATE INDEX IX_InventoryAnalytics_ProductId ON InventoryAnalytics(productId);
    CREATE INDEX IX_InventoryAnalytics_CategoryId ON InventoryAnalytics(categoryId);
    CREATE INDEX IX_InventoryAnalytics_StockValue ON InventoryAnalytics(stockValue);

    PRINT 'InventoryAnalytics table created successfully';
END
ELSE
BEGIN
    PRINT 'InventoryAnalytics table already exists';
END
GO

-- Create UserActivityLogs table for tracking user actions
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserActivityLogs' AND xtype='U')
BEGIN
    CREATE TABLE UserActivityLogs (
        id INT PRIMARY KEY IDENTITY(1,1),
        userId INT NULL, -- NULL for guest users
        sessionId NVARCHAR(255) NULL,
        action NVARCHAR(100) NOT NULL, -- 'login', 'logout', 'view_product', 'add_to_cart', 'purchase', etc.
        entityType NVARCHAR(50) NULL, -- 'product', 'order', 'user', etc.
        entityId INT NULL,
        details NVARCHAR(MAX) NULL, -- JSON with additional details
        ipAddress NVARCHAR(45) NULL,
        userAgent NVARCHAR(500) NULL,
        referrer NVARCHAR(500) NULL,
        duration INT NULL, -- Duration in seconds for timed actions
        createdAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_UserActivityLogs_Users FOREIGN KEY (userId) REFERENCES Users(id)
    );

    -- Create indexes
    CREATE INDEX IX_UserActivityLogs_UserId ON UserActivityLogs(userId);
    CREATE INDEX IX_UserActivityLogs_SessionId ON UserActivityLogs(sessionId);
    CREATE INDEX IX_UserActivityLogs_Action ON UserActivityLogs(action);
    CREATE INDEX IX_UserActivityLogs_EntityType ON UserActivityLogs(entityType, entityId);
    CREATE INDEX IX_UserActivityLogs_CreatedAt ON UserActivityLogs(createdAt);

    PRINT 'UserActivityLogs table created successfully';
END
ELSE
BEGIN
    PRINT 'UserActivityLogs table already exists';
END
GO

-- Create CustomerAnalytics table for customer insights
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CustomerAnalytics' AND xtype='U')
BEGIN
    CREATE TABLE CustomerAnalytics (
        id INT PRIMARY KEY IDENTITY(1,1),
        userId INT NOT NULL,
        totalOrders INT DEFAULT 0,
        totalSpent DECIMAL(12,2) DEFAULT 0,
        averageOrderValue DECIMAL(10,2) DEFAULT 0,
        firstOrderDate DATETIME NULL,
        lastOrderDate DATETIME NULL,
        daysSinceLastOrder AS (DATEDIFF(day, lastOrderDate, GETDATE())) PERSISTED,
        customerLifetimeValue DECIMAL(12,2) DEFAULT 0,
        preferredCategory NVARCHAR(100) NULL,
        loyaltyScore DECIMAL(5,2) DEFAULT 0, -- 0-100 score
        riskScore DECIMAL(5,2) DEFAULT 0, -- Churn risk 0-100
        segmentType NVARCHAR(50) NULL, -- 'VIP', 'Regular', 'At-Risk', 'New'
        lastActivityDate DATETIME NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_CustomerAnalytics_Users FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE CASCADE,
        CONSTRAINT UQ_CustomerAnalytics_UserId UNIQUE (userId)
    );

    -- Create indexes
    CREATE INDEX IX_CustomerAnalytics_TotalSpent ON CustomerAnalytics(totalSpent);
    CREATE INDEX IX_CustomerAnalytics_SegmentType ON CustomerAnalytics(segmentType);
    CREATE INDEX IX_CustomerAnalytics_LoyaltyScore ON CustomerAnalytics(loyaltyScore);
    CREATE INDEX IX_CustomerAnalytics_RiskScore ON CustomerAnalytics(riskScore);
    CREATE INDEX IX_CustomerAnalytics_LastOrderDate ON CustomerAnalytics(lastOrderDate);

    PRINT 'CustomerAnalytics table created successfully';
END
ELSE
BEGIN
    PRINT 'CustomerAnalytics table already exists';
END
GO

-- Create ProductPerformance view for quick analytics
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'ProductPerformance')
BEGIN
    EXEC('
    CREATE VIEW ProductPerformance AS
    SELECT
        p.id as productId,
        p.name as productName,
        c.name as categoryName,
        ISNULL(SUM(oi.quantity), 0) as totalSold,
        ISNULL(SUM(oi.totalPrice), 0) as totalRevenue,
        ISNULL(AVG(oi.unitPrice), 0) as averagePrice,
        COUNT(DISTINCT o.userId) as uniqueCustomers,
        ISNULL(i.quantityOnHand, 0) as currentStock,
        ISNULL(i.quantityReserved, 0) as reservedStock,
        ISNULL(i.quantityAvailable, 0) as availableStock,
        CASE
            WHEN ISNULL(i.quantityAvailable, 0) <= i.reorderLevel THEN 1
            ELSE 0
        END as isLowStock,
        p.createdAt as productCreatedAt
    FROM Products p
    LEFT JOIN Categories c ON p.categoryId = c.id
    LEFT JOIN OrderItems oi ON p.id = oi.productId
    LEFT JOIN Orders o ON oi.orderId = o.id AND o.status NOT IN (''cancelled'', ''returned'')
    LEFT JOIN Inventory i ON p.id = i.productId
    WHERE p.inStock = 1
    GROUP BY p.id, p.name, c.name, i.quantityOnHand, i.quantityReserved,
             i.quantityAvailable, i.reorderLevel, p.createdAt
    ');

    PRINT 'ProductPerformance view created successfully';
END
ELSE
BEGIN
    PRINT 'ProductPerformance view already exists';
END
GO

-- Create DashboardMetrics view for admin dashboard
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'DashboardMetrics')
BEGIN
    EXEC('
    CREATE VIEW DashboardMetrics AS
    SELECT
        -- Order metrics
        (SELECT COUNT(*) FROM Orders WHERE CAST(createdAt AS DATE) = CAST(GETDATE() AS DATE)) as todayOrders,
        (SELECT COUNT(*) FROM Orders WHERE createdAt >= DATEADD(day, -7, GETDATE())) as weekOrders,
        (SELECT COUNT(*) FROM Orders WHERE createdAt >= DATEADD(month, -1, GETDATE())) as monthOrders,

        -- Revenue metrics
        (SELECT ISNULL(SUM(totalAmount), 0) FROM Orders WHERE CAST(createdAt AS DATE) = CAST(GETDATE() AS DATE) AND status NOT IN (''cancelled'', ''returned'')) as todayRevenue,
        (SELECT ISNULL(SUM(totalAmount), 0) FROM Orders WHERE createdAt >= DATEADD(day, -7, GETDATE()) AND status NOT IN (''cancelled'', ''returned'')) as weekRevenue,
        (SELECT ISNULL(SUM(totalAmount), 0) FROM Orders WHERE createdAt >= DATEADD(month, -1, GETDATE()) AND status NOT IN (''cancelled'', ''returned'')) as monthRevenue,

        -- Inventory metrics
        (SELECT COUNT(*) FROM Inventory WHERE quantityAvailable <= reorderLevel) as lowStockItems,
        (SELECT COUNT(*) FROM Inventory WHERE quantityAvailable = 0) as outOfStockItems,
        (SELECT ISNULL(SUM(quantityOnHand * unitCost), 0) FROM Inventory WHERE unitCost IS NOT NULL) as totalInventoryValue,

        -- Customer metrics
        (SELECT COUNT(*) FROM Users WHERE CAST(createdAt AS DATE) = CAST(GETDATE() AS DATE)) as newCustomersToday,
        (SELECT COUNT(*) FROM Users WHERE createdAt >= DATEADD(day, -7, GETDATE())) as newCustomersWeek,
        (SELECT COUNT(DISTINCT userId) FROM Orders WHERE createdAt >= DATEADD(day, -30, GETDATE())) as activeCustomersMonth
    ');

    PRINT 'DashboardMetrics view created successfully';
END
ELSE
BEGIN
    PRINT 'DashboardMetrics view already exists';
END
GO
