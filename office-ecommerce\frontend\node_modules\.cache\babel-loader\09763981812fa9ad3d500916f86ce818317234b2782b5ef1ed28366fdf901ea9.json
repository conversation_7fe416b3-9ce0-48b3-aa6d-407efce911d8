{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\Gallery.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport '../styles/gallery.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Gallery = () => {\n  _s();\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'masonry'\n  const galleryRef = useRef(null);\n  const categories = [{\n    id: 'all',\n    name: 'All',\n    icon: '🏢',\n    count: 12\n  }, {\n    id: 'desks',\n    name: 'Desks',\n    icon: '🖥️',\n    count: 4\n  }, {\n    id: 'chairs',\n    name: 'Chairs',\n    icon: '🪑',\n    count: 3\n  }, {\n    id: 'storage',\n    name: 'Storage',\n    icon: '📚',\n    count: 2\n  }, {\n    id: 'conference',\n    name: 'Conference',\n    icon: '🤝',\n    count: 2\n  }, {\n    id: 'workspace',\n    name: 'Workspace',\n    icon: '💼',\n    count: 1\n  }];\n  const galleryImages = [{\n    id: 1,\n    src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',\n    category: 'desks',\n    title: 'Executive Mahogany Desk',\n    description: 'Premium executive workspace with rich mahogany finish',\n    tags: ['executive', 'mahogany', 'premium'],\n    featured: true,\n    size: 'large'\n  }, {\n    id: 2,\n    src: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=600&h=800&fit=crop',\n    category: 'desks',\n    title: 'Modern Glass Desk',\n    description: 'Contemporary design meets functionality',\n    tags: ['modern', 'glass', 'contemporary'],\n    featured: false,\n    size: 'medium'\n  }, {\n    id: 3,\n    src: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop',\n    category: 'chairs',\n    title: 'Ergonomic Executive Chair',\n    description: 'Comfort and style combined for long work sessions',\n    tags: ['ergonomic', 'executive', 'comfort'],\n    featured: true,\n    size: 'medium'\n  }, {\n    id: 4,\n    src: 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=600&h=600&fit=crop',\n    category: 'storage',\n    title: 'Executive Filing Cabinet',\n    description: 'Organized storage solutions for modern offices',\n    tags: ['filing', 'storage', 'organization'],\n    featured: false,\n    size: 'small'\n  }, {\n    id: 5,\n    src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=500&fit=crop',\n    category: 'conference',\n    title: 'Conference Room Setup',\n    description: 'Professional meeting spaces that inspire collaboration',\n    tags: ['conference', 'meeting', 'collaboration'],\n    featured: true,\n    size: 'large'\n  }, {\n    id: 6,\n    src: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=600&h=700&fit=crop',\n    category: 'desks',\n    title: 'Standing Desk Setup',\n    description: 'Health-conscious workspace for active professionals',\n    tags: ['standing', 'health', 'adjustable'],\n    featured: false,\n    size: 'medium'\n  }, {\n    id: 7,\n    src: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=600&h=400&fit=crop',\n    category: 'chairs',\n    title: 'Modern Office Chairs',\n    description: 'Contemporary seating solutions for every workspace',\n    tags: ['modern', 'seating', 'contemporary'],\n    featured: false,\n    size: 'medium'\n  }, {\n    id: 8,\n    src: 'https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=600&h=800&fit=crop',\n    category: 'storage',\n    title: 'Modern Bookshelf',\n    description: 'Stylish storage and display for office libraries',\n    tags: ['bookshelf', 'display', 'modern'],\n    featured: false,\n    size: 'medium'\n  }, {\n    id: 9,\n    src: 'https://images.unsplash.com/photo-1497366412874-3415097a27e7?w=800&h=600&fit=crop',\n    category: 'workspace',\n    title: 'Complete Office Setup',\n    description: 'Fully furnished modern office workspace',\n    tags: ['complete', 'modern', 'workspace'],\n    featured: true,\n    size: 'large'\n  }, {\n    id: 10,\n    src: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=600&fit=crop',\n    category: 'chairs',\n    title: 'Mesh Task Chair',\n    description: 'Breathable mesh design for all-day comfort',\n    tags: ['mesh', 'task', 'breathable'],\n    featured: false,\n    size: 'small'\n  }, {\n    id: 11,\n    src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=500&fit=crop',\n    category: 'desks',\n    title: 'L-Shaped Executive Desk',\n    description: 'Spacious corner desk for maximum productivity',\n    tags: ['l-shaped', 'corner', 'spacious'],\n    featured: false,\n    size: 'medium'\n  }, {\n    id: 12,\n    src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop',\n    category: 'conference',\n    title: 'Boardroom Table',\n    description: 'Impressive boardroom table for executive meetings',\n    tags: ['boardroom', 'executive', 'meetings'],\n    featured: false,\n    size: 'large'\n  }];\n\n  // Enhanced filtering with search\n  const filteredImages = galleryImages.filter(img => {\n    const matchesCategory = selectedCategory === 'all' || img.category === selectedCategory;\n    const matchesSearch = searchTerm === '' || img.title.toLowerCase().includes(searchTerm.toLowerCase()) || img.description.toLowerCase().includes(searchTerm.toLowerCase()) || img.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    return matchesCategory && matchesSearch;\n  });\n\n  // Loading simulation\n  useEffect(() => {\n    setIsLoading(true);\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 800);\n    return () => clearTimeout(timer);\n  }, [selectedCategory, searchTerm]);\n\n  // Intersection Observer for animations\n  useEffect(() => {\n    var _galleryRef$current;\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('animate-in');\n        }\n      });\n    }, {\n      threshold: 0.1\n    });\n    const galleryItems = (_galleryRef$current = galleryRef.current) === null || _galleryRef$current === void 0 ? void 0 : _galleryRef$current.querySelectorAll('.gallery-item');\n    galleryItems === null || galleryItems === void 0 ? void 0 : galleryItems.forEach(item => observer.observe(item));\n    return () => observer.disconnect();\n  }, [filteredImages]);\n  const openModal = image => {\n    setSelectedImage(image);\n    document.body.style.overflow = 'hidden';\n  };\n  const closeModal = () => {\n    setSelectedImage(null);\n    document.body.style.overflow = 'unset';\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Escape') closeModal();\n  };\n  useEffect(() => {\n    document.addEventListener('keydown', handleKeyPress);\n    return () => document.removeEventListener('keydown', handleKeyPress);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"gallery-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"gallery-hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-background\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-overlay\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"breadcrumb\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 22V12H15V22\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 33\n              }, this), \"Home\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"/\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Gallery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"title-main\",\n              children: \"Design Gallery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"title-sub\",\n              children: \"Where Inspiration Meets Innovation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-description\",\n            children: \"Discover our curated collection of premium office furniture showcased in real workspace environments. From executive suites to collaborative spaces, find the perfect pieces to transform your office.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-number\",\n                children: galleryImages.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"Showcase Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-number\",\n                children: categories.length - 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-number\",\n                children: \"100+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"gallery-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"search-icon\",\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"11\",\n                  cy: \"11\",\n                  r: \"8\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M21 21L16.65 16.65\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search gallery...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"search-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 33\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"clear-search\",\n                onClick: () => setSearchTerm(''),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"18\",\n                    y1: \"6\",\n                    x2: \"6\",\n                    y2: \"18\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"6\",\n                    y1: \"6\",\n                    x2: \"18\",\n                    y2: \"18\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"view-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `view-btn ${viewMode === 'grid' ? 'active' : ''}`,\n              onClick: () => setViewMode('grid'),\n              title: \"Grid View\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"14\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"14\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `view-btn ${viewMode === 'masonry' ? 'active' : ''}`,\n              onClick: () => setViewMode('masonry'),\n              title: \"Masonry View\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"9\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"5\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"12\",\n                  width: \"7\",\n                  height: \"9\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"16\",\n                  width: \"7\",\n                  height: \"5\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-categories\",\n          children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `category-btn ${selectedCategory === category.id ? 'active' : ''}`,\n            onClick: () => setSelectedCategory(category.id),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"category-icon\",\n              children: category.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"category-name\",\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"category-count\",\n              children: category.count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 33\n            }, this)]\n          }, category.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"results-count\",\n            children: isLoading ? 'Loading...' : `${filteredImages.length} ${filteredImages.length === 1 ? 'image' : 'images'} found`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 25\n          }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"search-term\",\n            children: [\"for \\\"\", searchTerm, \"\\\"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: `gallery-grid ${viewMode}`,\n        ref: galleryRef,\n        children: isLoading ?\n        // Loading skeleton\n        Array.from({\n          length: 8\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gallery-item skeleton\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skeleton-title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skeleton-description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 33\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 29\n        }, this)) : filteredImages.length > 0 ? filteredImages.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `gallery-item ${image.size} ${image.featured ? 'featured' : ''}`,\n          onClick: () => openModal(image),\n          style: {\n            animationDelay: `${index * 0.1}s`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: image.src,\n              alt: image.title,\n              loading: \"lazy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 37\n            }, this), image.featured && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"featured-badge\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: /*#__PURE__*/_jsxDEV(\"polygon\", {\n                  points: \"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\",\n                  fill: \"currentColor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 45\n              }, this), \"Featured\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-overlay\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overlay-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"image-title\",\n                  children: image.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"image-description\",\n                  children: image.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"image-tags\",\n                  children: image.tags.slice(0, 3).map(tag => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"tag\",\n                    children: [\"#\", tag]\n                  }, tag, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 53\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overlay-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn primary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 378,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"3\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 379,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 53\n                    }, this), \"View Details\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn secondary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M19 14C19.5523 14 20 13.5523 20 13C20 12.4477 19.5523 12 19 12C18.4477 12 18 12.4477 18 13C18 13.5523 18.4477 14 19 14Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 385,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M5 14C5.55228 14 6 13.5523 6 13C6 12.4477 5.55228 12 5 12C4.44772 12 4 12.4477 4 13C4 13.5523 4.44772 14 5 14Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 386,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 14C12.5523 14 13 13.5523 13 13C13 12.4477 12.5523 12 12 12C11.4477 12 11 12.4477 11 13C11 13.5523 11.4477 14 12 14Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 387,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 53\n                    }, this), \"More\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 33\n          }, this)\n        }, image.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 29\n        }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-results\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-results-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"64\",\n              height: \"64\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"11\",\n                cy: \"11\",\n                r: \"8\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M21 21L16.65 16.65\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No images found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Try adjusting your search terms or category filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-outline\",\n            onClick: () => {\n              setSearchTerm('');\n              setSelectedCategory('all');\n            },\n            children: \"Clear Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"gallery-cta\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-background\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cta-pattern\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cta-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Ready to Transform Your Workspace?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get inspired by our gallery and discover the perfect furniture pieces to create your ideal office environment. From executive suites to collaborative spaces, we have everything you need.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cta-features\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 6L9 17L4 12\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Premium Quality\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 6L9 17L4 12\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Custom Solutions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 6L9 17L4 12\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Expert Support\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cta-actions\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              className: \"btn btn-primary btn-large\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M3 6H21\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M16 10C16 11.0609 15.5786 12.0783 14.8284 12.8284C14.0783 13.5786 13.0609 14 12 14C10.9391 14 9.92172 13.5786 9.17157 12.8284C8.42143 12.0783 8 11.0609 8 10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 33\n              }, this), \"Shop Collection\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              className: \"btn btn-outline btn-large\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 33\n              }, this), \"Get Consultation\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 13\n    }, this), selectedImage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: closeModal,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: closeModal,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [/*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"18\",\n              y1: \"6\",\n              x2: \"6\",\n              y2: \"18\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"6\",\n              y1: \"6\",\n              x2: \"18\",\n              y2: \"18\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-image\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: selectedImage.src,\n            alt: selectedImage.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 29\n          }, this), selectedImage.featured && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-featured-badge\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"polygon\", {\n                points: \"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\",\n                fill: \"currentColor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 37\n            }, this), \"Featured\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: selectedImage.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"modal-category\",\n              children: selectedImage.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"modal-description\",\n            children: selectedImage.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-tags\",\n            children: selectedImage.tags.map(tag => /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"modal-tag\",\n              children: [\"#\", tag]\n            }, tag, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-actions\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              className: \"btn btn-primary\",\n              onClick: closeModal,\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M3 6H21\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 37\n              }, this), \"View Products\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-outline\",\n              onClick: closeModal,\n              children: \"Close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 203,\n    columnNumber: 9\n  }, this);\n};\n_s(Gallery, \"bpzD2AgrN4HQGY5afK+JzNe/4tI=\");\n_c = Gallery;\nexport default Gallery;\nvar _c;\n$RefreshReg$(_c, \"Gallery\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "jsxDEV", "_jsxDEV", "Gallery", "_s", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedImage", "setSelectedImage", "isLoading", "setIsLoading", "searchTerm", "setSearchTerm", "viewMode", "setViewMode", "galleryRef", "categories", "id", "name", "icon", "count", "galleryImages", "src", "category", "title", "description", "tags", "featured", "size", "filteredImages", "filter", "img", "matchesCategory", "matchesSearch", "toLowerCase", "includes", "some", "tag", "timer", "setTimeout", "clearTimeout", "_galleryRef$current", "observer", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "target", "classList", "add", "threshold", "galleryItems", "current", "querySelectorAll", "item", "observe", "disconnect", "openModal", "image", "document", "body", "style", "overflow", "closeModal", "handleKeyPress", "e", "key", "addEventListener", "removeEventListener", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "length", "cx", "cy", "r", "type", "placeholder", "value", "onChange", "onClick", "x1", "y1", "x2", "y2", "x", "y", "map", "ref", "Array", "from", "_", "index", "animationDelay", "alt", "loading", "points", "slice", "strokeLinecap", "strokeLinejoin", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/Gallery.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport '../styles/gallery.css';\n\nconst Gallery = () => {\n    const [selectedCategory, setSelectedCategory] = useState('all');\n    const [selectedImage, setSelectedImage] = useState(null);\n    const [isLoading, setIsLoading] = useState(true);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'masonry'\n    const galleryRef = useRef(null);\n\n    const categories = [\n        { id: 'all', name: 'All', icon: '🏢', count: 12 },\n        { id: 'desks', name: 'Desks', icon: '🖥️', count: 4 },\n        { id: 'chairs', name: 'Chairs', icon: '🪑', count: 3 },\n        { id: 'storage', name: 'Storage', icon: '📚', count: 2 },\n        { id: 'conference', name: 'Conference', icon: '🤝', count: 2 },\n        { id: 'workspace', name: 'Workspace', icon: '💼', count: 1 }\n    ];\n\n    const galleryImages = [\n        {\n            id: 1,\n            src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',\n            category: 'desks',\n            title: 'Executive Mahogany Desk',\n            description: 'Premium executive workspace with rich mahogany finish',\n            tags: ['executive', 'mahogany', 'premium'],\n            featured: true,\n            size: 'large'\n        },\n        {\n            id: 2,\n            src: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=600&h=800&fit=crop',\n            category: 'desks',\n            title: 'Modern Glass Desk',\n            description: 'Contemporary design meets functionality',\n            tags: ['modern', 'glass', 'contemporary'],\n            featured: false,\n            size: 'medium'\n        },\n        {\n            id: 3,\n            src: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop',\n            category: 'chairs',\n            title: 'Ergonomic Executive Chair',\n            description: 'Comfort and style combined for long work sessions',\n            tags: ['ergonomic', 'executive', 'comfort'],\n            featured: true,\n            size: 'medium'\n        },\n        {\n            id: 4,\n            src: 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=600&h=600&fit=crop',\n            category: 'storage',\n            title: 'Executive Filing Cabinet',\n            description: 'Organized storage solutions for modern offices',\n            tags: ['filing', 'storage', 'organization'],\n            featured: false,\n            size: 'small'\n        },\n        {\n            id: 5,\n            src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=500&fit=crop',\n            category: 'conference',\n            title: 'Conference Room Setup',\n            description: 'Professional meeting spaces that inspire collaboration',\n            tags: ['conference', 'meeting', 'collaboration'],\n            featured: true,\n            size: 'large'\n        },\n        {\n            id: 6,\n            src: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=600&h=700&fit=crop',\n            category: 'desks',\n            title: 'Standing Desk Setup',\n            description: 'Health-conscious workspace for active professionals',\n            tags: ['standing', 'health', 'adjustable'],\n            featured: false,\n            size: 'medium'\n        },\n        {\n            id: 7,\n            src: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=600&h=400&fit=crop',\n            category: 'chairs',\n            title: 'Modern Office Chairs',\n            description: 'Contemporary seating solutions for every workspace',\n            tags: ['modern', 'seating', 'contemporary'],\n            featured: false,\n            size: 'medium'\n        },\n        {\n            id: 8,\n            src: 'https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=600&h=800&fit=crop',\n            category: 'storage',\n            title: 'Modern Bookshelf',\n            description: 'Stylish storage and display for office libraries',\n            tags: ['bookshelf', 'display', 'modern'],\n            featured: false,\n            size: 'medium'\n        },\n        {\n            id: 9,\n            src: 'https://images.unsplash.com/photo-1497366412874-3415097a27e7?w=800&h=600&fit=crop',\n            category: 'workspace',\n            title: 'Complete Office Setup',\n            description: 'Fully furnished modern office workspace',\n            tags: ['complete', 'modern', 'workspace'],\n            featured: true,\n            size: 'large'\n        },\n        {\n            id: 10,\n            src: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=600&fit=crop',\n            category: 'chairs',\n            title: 'Mesh Task Chair',\n            description: 'Breathable mesh design for all-day comfort',\n            tags: ['mesh', 'task', 'breathable'],\n            featured: false,\n            size: 'small'\n        },\n        {\n            id: 11,\n            src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=500&fit=crop',\n            category: 'desks',\n            title: 'L-Shaped Executive Desk',\n            description: 'Spacious corner desk for maximum productivity',\n            tags: ['l-shaped', 'corner', 'spacious'],\n            featured: false,\n            size: 'medium'\n        },\n        {\n            id: 12,\n            src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop',\n            category: 'conference',\n            title: 'Boardroom Table',\n            description: 'Impressive boardroom table for executive meetings',\n            tags: ['boardroom', 'executive', 'meetings'],\n            featured: false,\n            size: 'large'\n        }\n    ];\n\n    // Enhanced filtering with search\n    const filteredImages = galleryImages.filter(img => {\n        const matchesCategory = selectedCategory === 'all' || img.category === selectedCategory;\n        const matchesSearch = searchTerm === '' ||\n            img.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n            img.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n            img.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        return matchesCategory && matchesSearch;\n    });\n\n    // Loading simulation\n    useEffect(() => {\n        setIsLoading(true);\n        const timer = setTimeout(() => {\n            setIsLoading(false);\n        }, 800);\n        return () => clearTimeout(timer);\n    }, [selectedCategory, searchTerm]);\n\n    // Intersection Observer for animations\n    useEffect(() => {\n        const observer = new IntersectionObserver(\n            (entries) => {\n                entries.forEach((entry) => {\n                    if (entry.isIntersecting) {\n                        entry.target.classList.add('animate-in');\n                    }\n                });\n            },\n            { threshold: 0.1 }\n        );\n\n        const galleryItems = galleryRef.current?.querySelectorAll('.gallery-item');\n        galleryItems?.forEach((item) => observer.observe(item));\n\n        return () => observer.disconnect();\n    }, [filteredImages]);\n\n    const openModal = (image) => {\n        setSelectedImage(image);\n        document.body.style.overflow = 'hidden';\n    };\n\n    const closeModal = () => {\n        setSelectedImage(null);\n        document.body.style.overflow = 'unset';\n    };\n\n    const handleKeyPress = (e) => {\n        if (e.key === 'Escape') closeModal();\n    };\n\n    useEffect(() => {\n        document.addEventListener('keydown', handleKeyPress);\n        return () => document.removeEventListener('keydown', handleKeyPress);\n    }, []);\n\n    return (\n        <div className=\"gallery-page\">\n            {/* Hero Section */}\n            <section className=\"gallery-hero\">\n                <div className=\"hero-background\">\n                    <div className=\"hero-overlay\"></div>\n                </div>\n                <div className=\"container\">\n                    <div className=\"hero-content\">\n                        <div className=\"breadcrumb\">\n                            <Link to=\"/\">\n                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <path d=\"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M9 22V12H15V22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                                Home\n                            </Link>\n                            <span>/</span>\n                            <span>Gallery</span>\n                        </div>\n                        <h1 className=\"hero-title\">\n                            <span className=\"title-main\">Design Gallery</span>\n                            <span className=\"title-sub\">Where Inspiration Meets Innovation</span>\n                        </h1>\n                        <p className=\"hero-description\">\n                            Discover our curated collection of premium office furniture showcased in real workspace environments.\n                            From executive suites to collaborative spaces, find the perfect pieces to transform your office.\n                        </p>\n                        <div className=\"hero-stats\">\n                            <div className=\"stat\">\n                                <span className=\"stat-number\">{galleryImages.length}</span>\n                                <span className=\"stat-label\">Showcase Images</span>\n                            </div>\n                            <div className=\"stat\">\n                                <span className=\"stat-number\">{categories.length - 1}</span>\n                                <span className=\"stat-label\">Categories</span>\n                            </div>\n                            <div className=\"stat\">\n                                <span className=\"stat-number\">100+</span>\n                                <span className=\"stat-label\">Products</span>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </section>\n\n            <div className=\"container\">\n                {/* Search and Filters */}\n                <section className=\"gallery-controls\">\n                    <div className=\"controls-header\">\n                        <div className=\"search-container\">\n                            <div className=\"search-input-wrapper\">\n                                <svg className=\"search-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                                <input\n                                    type=\"text\"\n                                    placeholder=\"Search gallery...\"\n                                    value={searchTerm}\n                                    onChange={(e) => setSearchTerm(e.target.value)}\n                                    className=\"search-input\"\n                                />\n                                {searchTerm && (\n                                    <button\n                                        className=\"clear-search\"\n                                        onClick={() => setSearchTerm('')}\n                                    >\n                                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                            <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                            <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                        </svg>\n                                    </button>\n                                )}\n                            </div>\n                        </div>\n\n                        <div className=\"view-controls\">\n                            <button\n                                className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}\n                                onClick={() => setViewMode('grid')}\n                                title=\"Grid View\"\n                            >\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                            </button>\n                            <button\n                                className={`view-btn ${viewMode === 'masonry' ? 'active' : ''}`}\n                                onClick={() => setViewMode('masonry')}\n                                title=\"Masonry View\"\n                            >\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <rect x=\"3\" y=\"3\" width=\"7\" height=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"3\" width=\"7\" height=\"5\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"12\" width=\"7\" height=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"3\" y=\"16\" width=\"7\" height=\"5\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                            </button>\n                        </div>\n                    </div>\n\n                    <div className=\"filter-categories\">\n                        {categories.map(category => (\n                            <button\n                                key={category.id}\n                                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}\n                                onClick={() => setSelectedCategory(category.id)}\n                            >\n                                <span className=\"category-icon\">{category.icon}</span>\n                                <span className=\"category-name\">{category.name}</span>\n                                <span className=\"category-count\">{category.count}</span>\n                            </button>\n                        ))}\n                    </div>\n\n                    <div className=\"results-info\">\n                        <span className=\"results-count\">\n                            {isLoading ? 'Loading...' : `${filteredImages.length} ${filteredImages.length === 1 ? 'image' : 'images'} found`}\n                        </span>\n                        {searchTerm && (\n                            <span className=\"search-term\">for \"{searchTerm}\"</span>\n                        )}\n                    </div>\n                </section>\n\n                {/* Gallery Grid */}\n                <section className={`gallery-grid ${viewMode}`} ref={galleryRef}>\n                    {isLoading ? (\n                        // Loading skeleton\n                        Array.from({ length: 8 }).map((_, index) => (\n                            <div key={index} className=\"gallery-item skeleton\">\n                                <div className=\"skeleton-image\"></div>\n                                <div className=\"skeleton-content\">\n                                    <div className=\"skeleton-title\"></div>\n                                    <div className=\"skeleton-description\"></div>\n                                </div>\n                            </div>\n                        ))\n                    ) : filteredImages.length > 0 ? (\n                        filteredImages.map((image, index) => (\n                            <div\n                                key={image.id}\n                                className={`gallery-item ${image.size} ${image.featured ? 'featured' : ''}`}\n                                onClick={() => openModal(image)}\n                                style={{ animationDelay: `${index * 0.1}s` }}\n                            >\n                                <div className=\"image-container\">\n                                    <img\n                                        src={image.src}\n                                        alt={image.title}\n                                        loading=\"lazy\"\n                                    />\n                                    {image.featured && (\n                                        <div className=\"featured-badge\">\n                                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                                <polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\" fill=\"currentColor\"/>\n                                            </svg>\n                                            Featured\n                                        </div>\n                                    )}\n                                    <div className=\"image-overlay\">\n                                        <div className=\"overlay-content\">\n                                            <h3 className=\"image-title\">{image.title}</h3>\n                                            <p className=\"image-description\">{image.description}</p>\n                                            <div className=\"image-tags\">\n                                                {image.tags.slice(0, 3).map(tag => (\n                                                    <span key={tag} className=\"tag\">#{tag}</span>\n                                                ))}\n                                            </div>\n                                            <div className=\"overlay-actions\">\n                                                <button className=\"action-btn primary\">\n                                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                                        <path d=\"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                                    </svg>\n                                                    View Details\n                                                </button>\n                                                <button className=\"action-btn secondary\">\n                                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                                        <path d=\"M19 14C19.5523 14 20 13.5523 20 13C20 12.4477 19.5523 12 19 12C18.4477 12 18 12.4477 18 13C18 13.5523 18.4477 14 19 14Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                                        <path d=\"M5 14C5.55228 14 6 13.5523 6 13C6 12.4477 5.55228 12 5 12C4.44772 12 4 12.4477 4 13C4 13.5523 4.44772 14 5 14Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                                        <path d=\"M12 14C12.5523 14 13 13.5523 13 13C13 12.4477 12.5523 12 12 12C11.4477 12 11 12.4477 11 13C11 13.5523 11.4477 14 12 14Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                                    </svg>\n                                                    More\n                                                </button>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        ))\n                    ) : (\n                        <div className=\"no-results\">\n                            <div className=\"no-results-icon\">\n                                <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                            </div>\n                            <h3>No images found</h3>\n                            <p>Try adjusting your search terms or category filters</p>\n                            <button\n                                className=\"btn btn-outline\"\n                                onClick={() => {\n                                    setSearchTerm('');\n                                    setSelectedCategory('all');\n                                }}\n                            >\n                                Clear Filters\n                            </button>\n                        </div>\n                    )}\n                </section>\n\n                {/* Call to Action */}\n                <section className=\"gallery-cta\">\n                    <div className=\"cta-background\">\n                        <div className=\"cta-pattern\"></div>\n                    </div>\n                    <div className=\"cta-content\">\n                        <div className=\"cta-text\">\n                            <h2>Ready to Transform Your Workspace?</h2>\n                            <p>\n                                Get inspired by our gallery and discover the perfect furniture pieces\n                                to create your ideal office environment. From executive suites to\n                                collaborative spaces, we have everything you need.\n                            </p>\n                            <div className=\"cta-features\">\n                                <div className=\"feature\">\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                        <path d=\"M20 6L9 17L4 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                    </svg>\n                                    <span>Premium Quality</span>\n                                </div>\n                                <div className=\"feature\">\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                        <path d=\"M20 6L9 17L4 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                    </svg>\n                                    <span>Custom Solutions</span>\n                                </div>\n                                <div className=\"feature\">\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                        <path d=\"M20 6L9 17L4 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                    </svg>\n                                    <span>Expert Support</span>\n                                </div>\n                            </div>\n                        </div>\n                        <div className=\"cta-actions\">\n                            <Link to=\"/products\" className=\"btn btn-primary btn-large\">\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <path d=\"M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M3 6H21\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M16 10C16 11.0609 15.5786 12.0783 14.8284 12.8284C14.0783 13.5786 13.0609 14 12 14C10.9391 14 9.92172 13.5786 9.17157 12.8284C8.42143 12.0783 8 11.0609 8 10\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                                Shop Collection\n                            </Link>\n                            <Link to=\"/contact\" className=\"btn btn-outline btn-large\">\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <path d=\"M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                                Get Consultation\n                            </Link>\n                        </div>\n                    </div>\n                </section>\n            </div>\n\n            {/* Enhanced Modal */}\n            {selectedImage && (\n                <div className=\"modal-overlay\" onClick={closeModal}>\n                    <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n                        <button className=\"modal-close\" onClick={closeModal}>\n                            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                            </svg>\n                        </button>\n\n                        <div className=\"modal-image\">\n                            <img src={selectedImage.src} alt={selectedImage.title} />\n                            {selectedImage.featured && (\n                                <div className=\"modal-featured-badge\">\n                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                        <polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\" fill=\"currentColor\"/>\n                                    </svg>\n                                    Featured\n                                </div>\n                            )}\n                        </div>\n\n                        <div className=\"modal-info\">\n                            <div className=\"modal-header\">\n                                <h3>{selectedImage.title}</h3>\n                                <span className=\"modal-category\">{selectedImage.category}</span>\n                            </div>\n                            <p className=\"modal-description\">{selectedImage.description}</p>\n\n                            <div className=\"modal-tags\">\n                                {selectedImage.tags.map(tag => (\n                                    <span key={tag} className=\"modal-tag\">#{tag}</span>\n                                ))}\n                            </div>\n\n                            <div className=\"modal-actions\">\n                                <Link\n                                    to=\"/products\"\n                                    className=\"btn btn-primary\"\n                                    onClick={closeModal}\n                                >\n                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                        <path d=\"M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                        <path d=\"M3 6H21\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    </svg>\n                                    View Products\n                                </Link>\n                                <button className=\"btn btn-outline\" onClick={closeModal}>\n                                    Close\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default Gallery;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAClD,MAAMkB,UAAU,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMiB,UAAU,GAAG,CACf;IAAEC,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAG,CAAC,EACjD;IAAEH,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAE,CAAC,EACrD;IAAEH,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,EACtD;IAAEH,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,EACxD;IAAEH,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,EAC9D;IAAEH,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,CAC/D;EAED,MAAMC,aAAa,GAAG,CAClB;IACIJ,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,uDAAuD;IACpEC,IAAI,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC;IAC1CC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,gFAAgF;IACrFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,yCAAyC;IACtDC,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,cAAc,CAAC;IACzCC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,mDAAmD;IAChEC,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC;IAC3CC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,gDAAgD;IAC7DC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,cAAc,CAAC;IAC3CC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,YAAY;IACtBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,wDAAwD;IACrEC,IAAI,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,eAAe,CAAC;IAChDC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,qDAAqD;IAClEC,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC;IAC1CC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,oDAAoD;IACjEC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,cAAc,CAAC;IAC3CC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,kDAAkD;IAC/DC,IAAI,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,QAAQ,CAAC;IACxCC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,yCAAyC;IACtDC,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC;IACzCC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,EAAE;IACNK,GAAG,EAAE,gFAAgF;IACrFC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,4CAA4C;IACzDC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC;IACpCC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,EAAE;IACNK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,+CAA+C;IAC5DC,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;IACxCC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,EAAE;IACNK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,YAAY;IACtBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,mDAAmD;IAChEC,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC;IAC5CC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,CACJ;;EAED;EACA,MAAMC,cAAc,GAAGR,aAAa,CAACS,MAAM,CAACC,GAAG,IAAI;IAC/C,MAAMC,eAAe,GAAG3B,gBAAgB,KAAK,KAAK,IAAI0B,GAAG,CAACR,QAAQ,KAAKlB,gBAAgB;IACvF,MAAM4B,aAAa,GAAGtB,UAAU,KAAK,EAAE,IACnCoB,GAAG,CAACP,KAAK,CAACU,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAAC,IAC1DH,GAAG,CAACN,WAAW,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAAC,IAChEH,GAAG,CAACL,IAAI,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAAC,CAAC;IAC9E,OAAOF,eAAe,IAAIC,aAAa;EAC3C,CAAC,CAAC;;EAEF;EACAnC,SAAS,CAAC,MAAM;IACZY,YAAY,CAAC,IAAI,CAAC;IAClB,MAAM4B,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC3B7B,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC,EAAE,GAAG,CAAC;IACP,OAAO,MAAM8B,YAAY,CAACF,KAAK,CAAC;EACpC,CAAC,EAAE,CAACjC,gBAAgB,EAAEM,UAAU,CAAC,CAAC;;EAElC;EACAb,SAAS,CAAC,MAAM;IAAA,IAAA2C,mBAAA;IACZ,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CACpCC,OAAO,IAAK;MACTA,OAAO,CAACC,OAAO,CAAEC,KAAK,IAAK;QACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACtBD,KAAK,CAACE,MAAM,CAACC,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN,CAAC,EACD;MAAEC,SAAS,EAAE;IAAI,CACrB,CAAC;IAED,MAAMC,YAAY,IAAAX,mBAAA,GAAG1B,UAAU,CAACsC,OAAO,cAAAZ,mBAAA,uBAAlBA,mBAAA,CAAoBa,gBAAgB,CAAC,eAAe,CAAC;IAC1EF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEP,OAAO,CAAEU,IAAI,IAAKb,QAAQ,CAACc,OAAO,CAACD,IAAI,CAAC,CAAC;IAEvD,OAAO,MAAMb,QAAQ,CAACe,UAAU,CAAC,CAAC;EACtC,CAAC,EAAE,CAAC5B,cAAc,CAAC,CAAC;EAEpB,MAAM6B,SAAS,GAAIC,KAAK,IAAK;IACzBnD,gBAAgB,CAACmD,KAAK,CAAC;IACvBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EAC3C,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrBxD,gBAAgB,CAAC,IAAI,CAAC;IACtBoD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;EAC1C,CAAC;EAED,MAAME,cAAc,GAAIC,CAAC,IAAK;IAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAEH,UAAU,CAAC,CAAC;EACxC,CAAC;EAEDlE,SAAS,CAAC,MAAM;IACZ8D,QAAQ,CAACQ,gBAAgB,CAAC,SAAS,EAAEH,cAAc,CAAC;IACpD,OAAO,MAAML,QAAQ,CAACS,mBAAmB,CAAC,SAAS,EAAEJ,cAAc,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACI/D,OAAA;IAAKoE,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAEzBrE,OAAA;MAASoE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC7BrE,OAAA;QAAKoE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BrE,OAAA;UAAKoE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACNzE,OAAA;QAAKoE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBrE,OAAA;UAAKoE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBrE,OAAA;YAAKoE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBrE,OAAA,CAACF,IAAI;cAAC4E,EAAE,EAAC,GAAG;cAAAL,QAAA,gBACRrE,OAAA;gBAAK2E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvDrE,OAAA;kBAAM+E,CAAC,EAAC,8KAA8K;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9NzE,OAAA;kBAAM+E,CAAC,EAAC,gBAAgB;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,QAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzE,OAAA;cAAAqE,QAAA,EAAM;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACdzE,OAAA;cAAAqE,QAAA,EAAM;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNzE,OAAA;YAAIoE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACtBrE,OAAA;cAAMoE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDzE,OAAA;cAAMoE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACLzE,OAAA;YAAGoE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAGhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJzE,OAAA;YAAKoE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBrE,OAAA;cAAKoE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACjBrE,OAAA;gBAAMoE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAElD,aAAa,CAAC+D;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DzE,OAAA;gBAAMoE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNzE,OAAA;cAAKoE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACjBrE,OAAA;gBAAMoE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEvD,UAAU,CAACoE,MAAM,GAAG;cAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5DzE,OAAA;gBAAMoE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNzE,OAAA;cAAKoE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACjBrE,OAAA;gBAAMoE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCzE,OAAA;gBAAMoE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEVzE,OAAA;MAAKoE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAEtBrE,OAAA;QAASoE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACjCrE,OAAA;UAAKoE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BrE,OAAA;YAAKoE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7BrE,OAAA;cAAKoE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjCrE,OAAA;gBAAKoE,SAAS,EAAC,aAAa;gBAACO,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBAC/ErE,OAAA;kBAAQmF,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,GAAG;kBAACL,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACrEzE,OAAA;kBAAM+E,CAAC,EAAC,oBAAoB;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNzE,OAAA;gBACIsF,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,mBAAmB;gBAC/BC,KAAK,EAAE/E,UAAW;gBAClBgF,QAAQ,EAAGzB,CAAC,IAAKtD,aAAa,CAACsD,CAAC,CAAClB,MAAM,CAAC0C,KAAK,CAAE;gBAC/CpB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,EACDhE,UAAU,iBACPT,OAAA;gBACIoE,SAAS,EAAC,cAAc;gBACxBsB,OAAO,EAAEA,CAAA,KAAMhF,aAAa,CAAC,EAAE,CAAE;gBAAA2D,QAAA,eAEjCrE,OAAA;kBAAK2E,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAT,QAAA,gBACvDrE,OAAA;oBAAM2F,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,IAAI;oBAACd,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC3EzE,OAAA;oBAAM2F,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACd,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENzE,OAAA;YAAKoE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BrE,OAAA;cACIoE,SAAS,EAAE,YAAYzD,QAAQ,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC7D+E,OAAO,EAAEA,CAAA,KAAM9E,WAAW,CAAC,MAAM,CAAE;cACnCU,KAAK,EAAC,WAAW;cAAA+C,QAAA,eAEjBrE,OAAA;gBAAK2E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvDrE,OAAA;kBAAM+F,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9EzE,OAAA;kBAAM+F,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,GAAG;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/EzE,OAAA;kBAAM+F,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChFzE,OAAA;kBAAM+F,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,IAAI;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACTzE,OAAA;cACIoE,SAAS,EAAE,YAAYzD,QAAQ,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;cAChE+E,OAAO,EAAEA,CAAA,KAAM9E,WAAW,CAAC,SAAS,CAAE;cACtCU,KAAK,EAAC,cAAc;cAAA+C,QAAA,eAEpBrE,OAAA;gBAAK2E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvDrE,OAAA;kBAAM+F,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9EzE,OAAA;kBAAM+F,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,GAAG;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/EzE,OAAA;kBAAM+F,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChFzE,OAAA;kBAAM+F,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,IAAI;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENzE,OAAA;UAAKoE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC7BvD,UAAU,CAACmF,GAAG,CAAC5E,QAAQ,iBACpBrB,OAAA;YAEIoE,SAAS,EAAE,gBAAgBjE,gBAAgB,KAAKkB,QAAQ,CAACN,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC9E2E,OAAO,EAAEA,CAAA,KAAMtF,mBAAmB,CAACiB,QAAQ,CAACN,EAAE,CAAE;YAAAsD,QAAA,gBAEhDrE,OAAA;cAAMoE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEhD,QAAQ,CAACJ;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDzE,OAAA;cAAMoE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEhD,QAAQ,CAACL;YAAI;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDzE,OAAA;cAAMoE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAEhD,QAAQ,CAACH;YAAK;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GANnDpD,QAAQ,CAACN,EAAE;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOZ,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENzE,OAAA;UAAKoE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBrE,OAAA;YAAMoE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC1B9D,SAAS,GAAG,YAAY,GAAG,GAAGoB,cAAc,CAACuD,MAAM,IAAIvD,cAAc,CAACuD,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,QAAQ;UAAQ;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G,CAAC,EACNhE,UAAU,iBACPT,OAAA;YAAMoE,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,QAAK,EAAC5D,UAAU,EAAC,IAAC;UAAA;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGVzE,OAAA;QAASoE,SAAS,EAAE,gBAAgBzD,QAAQ,EAAG;QAACuF,GAAG,EAAErF,UAAW;QAAAwD,QAAA,EAC3D9D,SAAS;QACN;QACA4F,KAAK,CAACC,IAAI,CAAC;UAAElB,MAAM,EAAE;QAAE,CAAC,CAAC,CAACe,GAAG,CAAC,CAACI,CAAC,EAAEC,KAAK,kBACnCtG,OAAA;UAAiBoE,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAC9CrE,OAAA;YAAKoE,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtCzE,OAAA;YAAKoE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC7BrE,OAAA;cAAKoE,SAAS,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtCzE,OAAA;cAAKoE,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA,GALA6B,KAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CACR,CAAC,GACF9C,cAAc,CAACuD,MAAM,GAAG,CAAC,GACzBvD,cAAc,CAACsE,GAAG,CAAC,CAACxC,KAAK,EAAE6C,KAAK,kBAC5BtG,OAAA;UAEIoE,SAAS,EAAE,gBAAgBX,KAAK,CAAC/B,IAAI,IAAI+B,KAAK,CAAChC,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;UAC5EiE,OAAO,EAAEA,CAAA,KAAMlC,SAAS,CAACC,KAAK,CAAE;UAChCG,KAAK,EAAE;YAAE2C,cAAc,EAAE,GAAGD,KAAK,GAAG,GAAG;UAAI,CAAE;UAAAjC,QAAA,eAE7CrE,OAAA;YAAKoE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BrE,OAAA;cACIoB,GAAG,EAAEqC,KAAK,CAACrC,GAAI;cACfoF,GAAG,EAAE/C,KAAK,CAACnC,KAAM;cACjBmF,OAAO,EAAC;YAAM;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EACDhB,KAAK,CAAChC,QAAQ,iBACXzB,OAAA;cAAKoE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3BrE,OAAA;gBAAK2E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,eACvDrE,OAAA;kBAAS0G,MAAM,EAAC,2FAA2F;kBAAC5B,IAAI,EAAC;gBAAc;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChI,CAAC,YAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACR,eACDzE,OAAA;cAAKoE,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC1BrE,OAAA;gBAAKoE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC5BrE,OAAA;kBAAIoE,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEZ,KAAK,CAACnC;gBAAK;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9CzE,OAAA;kBAAGoE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAEZ,KAAK,CAAClC;gBAAW;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxDzE,OAAA;kBAAKoE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EACtBZ,KAAK,CAACjC,IAAI,CAACmF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAAC9D,GAAG,iBAC3BnC,OAAA;oBAAgBoE,SAAS,EAAC,KAAK;oBAAAC,QAAA,GAAC,GAAC,EAAClC,GAAG;kBAAA,GAA1BA,GAAG;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA8B,CAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNzE,OAAA;kBAAKoE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC5BrE,OAAA;oBAAQoE,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBAClCrE,OAAA;sBAAK2E,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAT,QAAA,gBACvDrE,OAAA;wBAAM+E,CAAC,EAAC,mDAAmD;wBAACC,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACnGzE,OAAA;wBAAQmF,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,GAAG;wBAACL,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC,gBAEV;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTzE,OAAA;oBAAQoE,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACpCrE,OAAA;sBAAK2E,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAT,QAAA,gBACvDrE,OAAA;wBAAM+E,CAAC,EAAC,yHAAyH;wBAACC,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACzKzE,OAAA;wBAAM+E,CAAC,EAAC,gHAAgH;wBAACC,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAChKzE,OAAA;wBAAM+E,CAAC,EAAC,yHAAyH;wBAACC,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxK,CAAC,QAEV;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC,GA/CDhB,KAAK,CAAC1C,EAAE;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgDZ,CACR,CAAC,gBAEFzE,OAAA;UAAKoE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBrE,OAAA;YAAKoE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC5BrE,OAAA;cAAK2E,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAT,QAAA,gBACvDrE,OAAA;gBAAQmF,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,GAAG;gBAACL,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC;cAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACrEzE,OAAA;gBAAM+E,CAAC,EAAC,oBAAoB;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC;cAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNzE,OAAA;YAAAqE,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBzE,OAAA;YAAAqE,QAAA,EAAG;UAAmD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1DzE,OAAA;YACIoE,SAAS,EAAC,iBAAiB;YAC3BsB,OAAO,EAAEA,CAAA,KAAM;cACXhF,aAAa,CAAC,EAAE,CAAC;cACjBN,mBAAmB,CAAC,KAAK,CAAC;YAC9B,CAAE;YAAAiE,QAAA,EACL;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGVzE,OAAA;QAASoE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC5BrE,OAAA;UAAKoE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC3BrE,OAAA;YAAKoE,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACNzE,OAAA;UAAKoE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBrE,OAAA;YAAKoE,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACrBrE,OAAA;cAAAqE,QAAA,EAAI;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CzE,OAAA;cAAAqE,QAAA,EAAG;YAIH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJzE,OAAA;cAAKoE,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBrE,OAAA;gBAAKoE,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACpBrE,OAAA;kBAAK2E,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAT,QAAA,eACvDrE,OAAA;oBAAM+E,CAAC,EAAC,iBAAiB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAAC2B,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC,eACNzE,OAAA;kBAAAqE,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACNzE,OAAA;gBAAKoE,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACpBrE,OAAA;kBAAK2E,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAT,QAAA,eACvDrE,OAAA;oBAAM+E,CAAC,EAAC,iBAAiB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAAC2B,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC,eACNzE,OAAA;kBAAAqE,QAAA,EAAM;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACNzE,OAAA;gBAAKoE,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACpBrE,OAAA;kBAAK2E,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAT,QAAA,eACvDrE,OAAA;oBAAM+E,CAAC,EAAC,iBAAiB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAAC2B,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC,eACNzE,OAAA;kBAAAqE,QAAA,EAAM;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNzE,OAAA;YAAKoE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxBrE,OAAA,CAACF,IAAI;cAAC4E,EAAE,EAAC,WAAW;cAACN,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACtDrE,OAAA;gBAAK2E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvDrE,OAAA;kBAAM+E,CAAC,EAAC,gLAAgL;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChOzE,OAAA;kBAAM+E,CAAC,EAAC,SAAS;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACzDzE,OAAA;kBAAM+E,CAAC,EAAC,8JAA8J;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7M,CAAC,mBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzE,OAAA,CAACF,IAAI;cAAC4E,EAAE,EAAC,UAAU;cAACN,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACrDrE,OAAA;gBAAK2E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,eACvDrE,OAAA;kBAAM+E,CAAC,EAAC,sPAAsP;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrS,CAAC,oBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,EAGLpE,aAAa,iBACVL,OAAA;MAAKoE,SAAS,EAAC,eAAe;MAACsB,OAAO,EAAE5B,UAAW;MAAAO,QAAA,eAC/CrE,OAAA;QAAKoE,SAAS,EAAC,eAAe;QAACsB,OAAO,EAAG1B,CAAC,IAAKA,CAAC,CAAC8C,eAAe,CAAC,CAAE;QAAAzC,QAAA,gBAC/DrE,OAAA;UAAQoE,SAAS,EAAC,aAAa;UAACsB,OAAO,EAAE5B,UAAW;UAAAO,QAAA,eAChDrE,OAAA;YAAK2E,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAT,QAAA,gBACvDrE,OAAA;cAAM2F,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACd,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC3EzE,OAAA;cAAM2F,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACd,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAETzE,OAAA;UAAKoE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBrE,OAAA;YAAKoB,GAAG,EAAEf,aAAa,CAACe,GAAI;YAACoF,GAAG,EAAEnG,aAAa,CAACiB;UAAM;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxDpE,aAAa,CAACoB,QAAQ,iBACnBzB,OAAA;YAAKoE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjCrE,OAAA;cAAK2E,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAT,QAAA,eACvDrE,OAAA;gBAAS0G,MAAM,EAAC,2FAA2F;gBAAC5B,IAAI,EAAC;cAAc;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC,YAEV;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAENzE,OAAA;UAAKoE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBrE,OAAA;YAAKoE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBrE,OAAA;cAAAqE,QAAA,EAAKhE,aAAa,CAACiB;YAAK;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9BzE,OAAA;cAAMoE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAEhE,aAAa,CAACgB;YAAQ;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNzE,OAAA;YAAGoE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAEhE,aAAa,CAACkB;UAAW;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEhEzE,OAAA;YAAKoE,SAAS,EAAC,YAAY;YAAAC,QAAA,EACtBhE,aAAa,CAACmB,IAAI,CAACyE,GAAG,CAAC9D,GAAG,iBACvBnC,OAAA;cAAgBoE,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAC,GAAC,EAAClC,GAAG;YAAA,GAAhCA,GAAG;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoC,CACrD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENzE,OAAA;YAAKoE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BrE,OAAA,CAACF,IAAI;cACD4E,EAAE,EAAC,WAAW;cACdN,SAAS,EAAC,iBAAiB;cAC3BsB,OAAO,EAAE5B,UAAW;cAAAO,QAAA,gBAEpBrE,OAAA;gBAAK2E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvDrE,OAAA;kBAAM+E,CAAC,EAAC,gLAAgL;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChOzE,OAAA;kBAAM+E,CAAC,EAAC,SAAS;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,iBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzE,OAAA;cAAQoE,SAAS,EAAC,iBAAiB;cAACsB,OAAO,EAAE5B,UAAW;cAAAO,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACvE,EAAA,CA/gBID,OAAO;AAAA8G,EAAA,GAAP9G,OAAO;AAihBb,eAAeA,OAAO;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}