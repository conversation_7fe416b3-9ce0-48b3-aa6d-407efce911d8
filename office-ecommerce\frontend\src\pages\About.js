import React from 'react';
import { Link } from 'react-router-dom';

const About = () => {
    return (
        <div className="about-page">
            <div className="container">
                <div className="breadcrumb">
                    <Link to="/">Home</Link> /
                    <span>About</span>
                </div>

                {/* Our Story Section */}
                <section className="our-story-section">
                    <div className="story-header">
                        <h1>Our Story</h1>
                        <p>
                            For over two decades, we've been crafting exceptional office environments that inspire
                            creativity, enhance productivity, and elevate the modern workplace experience.
                        </p>
                    </div>

                    <div className="story-content">
                        <div className="story-left">
                            <h2>Crafting Excellence Since 2000</h2>
                            <p>
                                What began as a small workshop has grown into a leading provider of
                                premium office furniture solutions. Our commitment to quality, innovation
                                and customer satisfaction has been our guiding principle.
                            </p>

                            <div className="stats-grid">
                                <div className="stat-item">
                                    <h3>2000+</h3>
                                    <p>Projects Completed</p>
                                </div>
                                <div className="stat-item">
                                    <h3>500+</h3>
                                    <p>Happy Clients</p>
                                </div>
                            </div>
                        </div>

                        <div className="story-right">
                            <div className="story-image">
                                <img
                                    src="https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop"
                                    alt="Modern office workspace"
                                />
                            </div>
                        </div>
                    </div>
                </section>

                {/* Our Mission Section */}
                <section className="our-mission-section">
                    <div className="mission-content">
                        <div className="mission-left">
                            <div className="mission-image">
                                <img
                                    src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop"
                                    alt="Office design consultation"
                                />
                            </div>
                        </div>

                        <div className="mission-right">
                            <h2>Our Mission</h2>
                            <p>
                                We strive to transform workspaces into inspiring environments where
                                creativity flourishes and productivity soars. Through innovative design and
                                unwavering attention to detail, we create furniture solutions that perfectly
                                balance form and function.
                            </p>

                            <div className="mission-features">
                                <div className="feature-item">
                                    <div className="feature-icon">✓</div>
                                    <span>Premium Materials</span>
                                </div>
                                <div className="feature-item">
                                    <div className="feature-icon">✓</div>
                                    <span>Ergonomic Design</span>
                                </div>
                                <div className="feature-item">
                                    <div className="feature-icon">✓</div>
                                    <span>Sustainable Practices</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Our Values Section */}
                <section className="our-values-section">
                    <div className="values-header">
                        <h2>Our Values</h2>
                    </div>

                    <div className="values-grid">
                        <div className="value-card">
                            <div className="value-icon innovation">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#F0B21B"/>
                                </svg>
                            </div>
                            <h3>Innovation</h3>
                            <p>Pushing boundaries in design and functionality</p>
                        </div>

                        <div className="value-card">
                            <div className="value-icon quality">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="3" y="3" width="18" height="18" rx="2" fill="#F0B21B"/>
                                    <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                            <h3>Quality</h3>
                            <p>Uncompromising commitment to excellence</p>
                        </div>

                        <div className="value-card">
                            <div className="value-icon service">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" fill="#F0B21B"/>
                                    <path d="M8 14S9.5 16 12 16S16 14 16 14" stroke="white" strokeWidth="2" strokeLinecap="round"/>
                                    <circle cx="9" cy="9" r="1" fill="white"/>
                                    <circle cx="15" cy="9" r="1" fill="white"/>
                                </svg>
                            </div>
                            <h3>Service</h3>
                            <p>Dedicated to exceeding expectations</p>
                        </div>

                        <div className="value-card">
                            <div className="value-icon sustainability">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L6.83 7.17L2 12L3.41 13.41L6.83 10L10.5 13.67L16.17 8L18.83 10.67L17 12.5L19 14.5L21 9Z" fill="#F0B21B"/>
                                </svg>
                            </div>
                            <h3>Sustainability</h3>
                            <p>Committed to environmental responsibility</p>
                        </div>
                    </div>
                </section>

            </div>
        </div>
    );
};

export default About;
