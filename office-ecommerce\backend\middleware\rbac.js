const { getPool, sql } = require('../config/database');

/**
 * Role-Based Access Control Middleware
 * Checks if user has required permissions to access a resource
 */

/**
 * Get user permissions from database
 * @param {number} userId - User ID
 * @returns {Array} Array of permission names
 */
const getUserPermissions = async (userId) => {
    try {
        const pool = getPool();
        const result = await pool.request()
            .input('userId', sql.Int, userId)
            .execute('sp_GetUserPermissions');
        
        return result.recordset.map(row => row.permissionName);
    } catch (error) {
        console.error('Error getting user permissions:', error);
        return [];
    }
};

/**
 * Check if user has specific permission
 * @param {number} userId - User ID
 * @param {string} permissionName - Permission name to check
 * @returns {boolean} True if user has permission
 */
const checkUserPermission = async (userId, permissionName) => {
    try {
        const pool = getPool();
        const result = await pool.request()
            .input('userId', sql.Int, userId)
            .input('permissionName', sql.NVarChar, permissionName)
            .execute('sp_CheckUserPermission');
        
        return result.recordset[0]?.hasPermission === true;
    } catch (error) {
        console.error('Error checking user permission:', error);
        return false;
    }
};

/**
 * Get user roles
 * @param {number} userId - User ID
 * @returns {Array} Array of role names
 */
const getUserRoles = async (userId) => {
    try {
        const pool = getPool();
        const result = await pool.request()
            .input('userId', sql.Int, userId)
            .query(`
                SELECT r.name as roleName
                FROM Users u
                INNER JOIN UserRoles ur ON u.id = ur.userId
                INNER JOIN Roles r ON ur.roleId = r.id
                WHERE u.id = @userId 
                AND u.isActive = 1 
                AND ur.isActive = 1 
                AND r.isActive = 1
            `);
        
        return result.recordset.map(row => row.roleName);
    } catch (error) {
        console.error('Error getting user roles:', error);
        return [];
    }
};

/**
 * Middleware to require specific permission
 * @param {string} permission - Required permission name
 * @returns {Function} Express middleware function
 */
const requirePermission = (permission) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                return res.status(401).json({
                    message: 'Authentication required',
                    code: 'AUTH_REQUIRED'
                });
            }

            const hasPermission = await checkUserPermission(req.user.id, permission);
            
            if (!hasPermission) {
                return res.status(403).json({
                    message: 'Insufficient permissions',
                    code: 'INSUFFICIENT_PERMISSIONS',
                    required: permission
                });
            }

            next();
        } catch (error) {
            console.error('Permission check error:', error);
            return res.status(500).json({
                message: 'Permission check failed',
                code: 'PERMISSION_CHECK_ERROR'
            });
        }
    };
};

/**
 * Middleware to require specific role
 * @param {string|Array} roles - Required role name(s)
 * @returns {Function} Express middleware function
 */
const requireRole = (roles) => {
    const requiredRoles = Array.isArray(roles) ? roles : [roles];
    
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                return res.status(401).json({
                    message: 'Authentication required',
                    code: 'AUTH_REQUIRED'
                });
            }

            const userRoles = await getUserRoles(req.user.id);
            const hasRole = requiredRoles.some(role => userRoles.includes(role));
            
            if (!hasRole) {
                return res.status(403).json({
                    message: 'Insufficient role privileges',
                    code: 'INSUFFICIENT_ROLE',
                    required: requiredRoles,
                    current: userRoles
                });
            }

            next();
        } catch (error) {
            console.error('Role check error:', error);
            return res.status(500).json({
                message: 'Role check failed',
                code: 'ROLE_CHECK_ERROR'
            });
        }
    };
};

/**
 * Middleware to require admin role
 */
const requireAdmin = requireRole('Admin');

/**
 * Middleware to require admin or employee role
 */
const requireStaff = requireRole(['Admin', 'Employee']);

/**
 * Middleware to attach user permissions to request
 */
const attachPermissions = async (req, res, next) => {
    try {
        if (req.user && req.user.id) {
            req.user.permissions = await getUserPermissions(req.user.id);
            req.user.roles = await getUserRoles(req.user.id);
        }
        next();
    } catch (error) {
        console.error('Error attaching permissions:', error);
        next(); // Continue without permissions
    }
};

/**
 * Check if user can access resource based on ownership
 * @param {string} resourceUserIdField - Field name containing the user ID in the resource
 * @param {string} adminPermission - Permission that allows admin access
 * @returns {Function} Express middleware function
 */
const requireOwnershipOrPermission = (resourceUserIdField = 'userId', adminPermission = null) => {
    return async (req, res, next) => {
        try {
            if (!req.user || !req.user.id) {
                return res.status(401).json({
                    message: 'Authentication required',
                    code: 'AUTH_REQUIRED'
                });
            }

            // Check if user has admin permission
            if (adminPermission) {
                const hasAdminPermission = await checkUserPermission(req.user.id, adminPermission);
                if (hasAdminPermission) {
                    return next();
                }
            }

            // Check ownership - this would need to be implemented based on the specific resource
            // For now, we'll assume the resource ID is in req.params and check against the database
            const resourceId = req.params.id;
            if (!resourceId) {
                return res.status(400).json({
                    message: 'Resource ID required',
                    code: 'RESOURCE_ID_REQUIRED'
                });
            }

            // This is a placeholder - actual implementation would depend on the resource type
            // You would query the specific table to check if the resource belongs to the user
            
            next();
        } catch (error) {
            console.error('Ownership check error:', error);
            return res.status(500).json({
                message: 'Ownership check failed',
                code: 'OWNERSHIP_CHECK_ERROR'
            });
        }
    };
};

module.exports = {
    getUserPermissions,
    checkUserPermission,
    getUserRoles,
    requirePermission,
    requireRole,
    requireAdmin,
    requireStaff,
    attachPermissions,
    requireOwnershipOrPermission
};
