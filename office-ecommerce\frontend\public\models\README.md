# 3D Models Directory

This directory contains GLB (GL Transmission Format Binary) files for the 3D configurator.

## Required GLB Files

Based on the product data, the following GLB files are expected:

### Office Desks
- `executive-desk.glb` - Executive <PERSON><PERSON> Desk
- `glass-desk.glb` - Modern Glass Executive Desk  
- `standing-desk.glb` - Standing Desk Converter
- `l-shaped-desk.glb` - L-Shaped Computer Desk
- `conference-table.glb` - Conference Table

### Office Chairs
- `executive-chair.glb` - Ergonomic Executive Chair
- `mesh-chair.glb` - Mesh Task Chair
- `mesh-task-chair.glb` - Modern Mesh Task Chair

### Storage Solutions
- `filing-cabinet.glb` - Executive Filing Cabinet
- `bookshelf.glb` - Modern Bookshelf Unit

### Accessories
- `desk-organizer.glb` - Desk Organizer Set

## GLB File Requirements

### Technical Specifications
- **Format**: GLB (Binary GLTF)
- **Max File Size**: 10MB per file (recommended)
- **Textures**: Embedded in GLB file
- **Optimization**: Use Draco compression if possible

### Model Guidelines
- **Scale**: Real-world scale (1 unit = 1 meter)
- **Origin**: Center the model at (0,0,0)
- **Orientation**: Face forward along negative Z-axis
- **Materials**: PBR materials preferred
- **Polygons**: Keep under 50k triangles for performance

## How to Add GLB Files

1. **Place GLB files** in this directory (`/public/models/`)
2. **Update product data** in `/src/services/products.js` if needed
3. **Test loading** by visiting product detail pages

## GLB Creation Tools

### Recommended Software
- **Blender** (Free) - Export with glTF 2.0 add-on
- **3ds Max** - With Babylon.js exporter
- **Maya** - With glTF exporter plugin
- **Cinema 4D** - With glTF export

### Online Converters
- **Sketchfab** - Upload and download as GLB
- **Clara.io** - Online 3D editor with GLB export
- **Poly Haven** - Free 3D models in GLB format

## Testing Your GLB Files

1. **Online Viewers**:
   - https://gltf-viewer.donmccurdy.com/
   - https://sandbox.babylonjs.com/

2. **Local Testing**:
   - Place file in `/public/models/`
   - Visit product page in browser
   - Check browser console for loading errors

## File Naming Convention

Use lowercase with hyphens:
- ✅ `executive-desk.glb`
- ✅ `mesh-task-chair.glb`
- ❌ `Executive_Desk.GLB`
- ❌ `meshTaskChair.glb`

## Performance Tips

- **Optimize textures**: Use 1024x1024 or smaller
- **Compress geometry**: Use Draco compression
- **Reduce materials**: Combine similar materials
- **LOD models**: Consider multiple detail levels
