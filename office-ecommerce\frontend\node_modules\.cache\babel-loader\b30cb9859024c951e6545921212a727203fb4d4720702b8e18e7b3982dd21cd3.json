{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\Gallery.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Gallery = () => {\n  _s();\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'masonry'\n  const galleryRef = useRef(null);\n  const categories = [{\n    id: 'all',\n    name: 'All',\n    icon: '🏢',\n    count: 12\n  }, {\n    id: 'desks',\n    name: 'Desks',\n    icon: '🖥️',\n    count: 4\n  }, {\n    id: 'chairs',\n    name: 'Chairs',\n    icon: '🪑',\n    count: 3\n  }, {\n    id: 'storage',\n    name: 'Storage',\n    icon: '📚',\n    count: 2\n  }, {\n    id: 'conference',\n    name: 'Conference',\n    icon: '🤝',\n    count: 2\n  }, {\n    id: 'workspace',\n    name: 'Workspace',\n    icon: '💼',\n    count: 1\n  }];\n  const galleryImages = [{\n    id: 1,\n    src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',\n    category: 'desks',\n    title: 'Executive Mahogany Desk',\n    description: 'Premium executive workspace with rich mahogany finish',\n    tags: ['executive', 'mahogany', 'premium'],\n    featured: true,\n    size: 'large'\n  }, {\n    id: 2,\n    src: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=600&h=800&fit=crop',\n    category: 'desks',\n    title: 'Modern Glass Desk',\n    description: 'Contemporary design meets functionality',\n    tags: ['modern', 'glass', 'contemporary'],\n    featured: false,\n    size: 'medium'\n  }, {\n    id: 3,\n    src: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop',\n    category: 'chairs',\n    title: 'Ergonomic Executive Chair',\n    description: 'Comfort and style combined for long work sessions',\n    tags: ['ergonomic', 'executive', 'comfort'],\n    featured: true,\n    size: 'medium'\n  }, {\n    id: 4,\n    src: 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=600&h=600&fit=crop',\n    category: 'storage',\n    title: 'Executive Filing Cabinet',\n    description: 'Organized storage solutions for modern offices',\n    tags: ['filing', 'storage', 'organization'],\n    featured: false,\n    size: 'small'\n  }, {\n    id: 5,\n    src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=500&fit=crop',\n    category: 'conference',\n    title: 'Conference Room Setup',\n    description: 'Professional meeting spaces that inspire collaboration',\n    tags: ['conference', 'meeting', 'collaboration'],\n    featured: true,\n    size: 'large'\n  }, {\n    id: 6,\n    src: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=600&h=700&fit=crop',\n    category: 'desks',\n    title: 'Standing Desk Setup',\n    description: 'Health-conscious workspace for active professionals',\n    tags: ['standing', 'health', 'adjustable'],\n    featured: false,\n    size: 'medium'\n  }, {\n    id: 7,\n    src: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=600&h=400&fit=crop',\n    category: 'chairs',\n    title: 'Modern Office Chairs',\n    description: 'Contemporary seating solutions for every workspace',\n    tags: ['modern', 'seating', 'contemporary'],\n    featured: false,\n    size: 'medium'\n  }, {\n    id: 8,\n    src: 'https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=600&h=800&fit=crop',\n    category: 'storage',\n    title: 'Modern Bookshelf',\n    description: 'Stylish storage and display for office libraries',\n    tags: ['bookshelf', 'display', 'modern'],\n    featured: false,\n    size: 'medium'\n  }, {\n    id: 9,\n    src: 'https://images.unsplash.com/photo-1497366412874-3415097a27e7?w=800&h=600&fit=crop',\n    category: 'workspace',\n    title: 'Complete Office Setup',\n    description: 'Fully furnished modern office workspace',\n    tags: ['complete', 'modern', 'workspace'],\n    featured: true,\n    size: 'large'\n  }, {\n    id: 10,\n    src: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=600&fit=crop',\n    category: 'chairs',\n    title: 'Mesh Task Chair',\n    description: 'Breathable mesh design for all-day comfort',\n    tags: ['mesh', 'task', 'breathable'],\n    featured: false,\n    size: 'small'\n  }, {\n    id: 11,\n    src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=500&fit=crop',\n    category: 'desks',\n    title: 'L-Shaped Executive Desk',\n    description: 'Spacious corner desk for maximum productivity',\n    tags: ['l-shaped', 'corner', 'spacious'],\n    featured: false,\n    size: 'medium'\n  }, {\n    id: 12,\n    src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop',\n    category: 'conference',\n    title: 'Boardroom Table',\n    description: 'Impressive boardroom table for executive meetings',\n    tags: ['boardroom', 'executive', 'meetings'],\n    featured: false,\n    size: 'large'\n  }];\n\n  // Enhanced filtering with search\n  const filteredImages = galleryImages.filter(img => {\n    const matchesCategory = selectedCategory === 'all' || img.category === selectedCategory;\n    const matchesSearch = searchTerm === '' || img.title.toLowerCase().includes(searchTerm.toLowerCase()) || img.description.toLowerCase().includes(searchTerm.toLowerCase()) || img.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n    return matchesCategory && matchesSearch;\n  });\n\n  // Loading simulation\n  useEffect(() => {\n    setIsLoading(true);\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 800);\n    return () => clearTimeout(timer);\n  }, [selectedCategory, searchTerm]);\n\n  // Intersection Observer for animations\n  useEffect(() => {\n    var _galleryRef$current;\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('animate-in');\n        }\n      });\n    }, {\n      threshold: 0.1\n    });\n    const galleryItems = (_galleryRef$current = galleryRef.current) === null || _galleryRef$current === void 0 ? void 0 : _galleryRef$current.querySelectorAll('.gallery-item');\n    galleryItems === null || galleryItems === void 0 ? void 0 : galleryItems.forEach(item => observer.observe(item));\n    return () => observer.disconnect();\n  }, [filteredImages]);\n  const openModal = image => {\n    setSelectedImage(image);\n    document.body.style.overflow = 'hidden';\n  };\n  const closeModal = () => {\n    setSelectedImage(null);\n    document.body.style.overflow = 'unset';\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Escape') closeModal();\n  };\n  useEffect(() => {\n    document.addEventListener('keydown', handleKeyPress);\n    return () => document.removeEventListener('keydown', handleKeyPress);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"gallery-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"gallery-hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-background\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-overlay\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"breadcrumb\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M9 22V12H15V22\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 33\n              }, this), \"Home\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"/\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Gallery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"hero-title\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"title-main\",\n              children: \"Design Gallery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"title-sub\",\n              children: \"Where Inspiration Meets Innovation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-description\",\n            children: \"Discover our curated collection of premium office furniture showcased in real workspace environments. From executive suites to collaborative spaces, find the perfect pieces to transform your office.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-stats\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-number\",\n                children: galleryImages.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"Showcase Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-number\",\n                children: categories.length - 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-number\",\n                children: \"100+\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat-label\",\n                children: \"Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"gallery-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"search-icon\",\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"11\",\n                  cy: \"11\",\n                  r: \"8\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M21 21L16.65 16.65\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search gallery...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"search-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 33\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"clear-search\",\n                onClick: () => setSearchTerm(''),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"18\",\n                    y1: \"6\",\n                    x2: \"6\",\n                    y2: \"18\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"6\",\n                    y1: \"6\",\n                    x2: \"18\",\n                    y2: \"18\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"view-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `view-btn ${viewMode === 'grid' ? 'active' : ''}`,\n              onClick: () => setViewMode('grid'),\n              title: \"Grid View\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"14\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"14\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `view-btn ${viewMode === 'masonry' ? 'active' : ''}`,\n              onClick: () => setViewMode('masonry'),\n              title: \"Masonry View\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"9\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"5\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"12\",\n                  width: \"7\",\n                  height: \"9\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"16\",\n                  width: \"7\",\n                  height: \"5\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-categories\",\n          children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `category-btn ${selectedCategory === category.id ? 'active' : ''}`,\n            onClick: () => setSelectedCategory(category.id),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"category-icon\",\n              children: category.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"category-name\",\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"category-count\",\n              children: category.count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 33\n            }, this)]\n          }, category.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"results-count\",\n            children: isLoading ? 'Loading...' : `${filteredImages.length} ${filteredImages.length === 1 ? 'image' : 'images'} found`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 25\n          }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"search-term\",\n            children: [\"for \\\"\", searchTerm, \"\\\"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"gallery-grid\",\n        children: filteredImages.map(image => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gallery-item\",\n          onClick: () => openModal(image),\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: image.src,\n            alt: image.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"gallery-overlay\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: image.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: image.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"view-btn\",\n              children: \"View Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 29\n          }, this)]\n        }, image.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 17\n      }, this), filteredImages.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-results\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No images found for the selected category.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"gallery-cta\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Inspired by What You See?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Browse our complete collection and find the perfect pieces for your workspace\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"btn btn-primary\",\n            children: \"Shop Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 13\n    }, this), selectedImage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: closeModal,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: closeModal,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: selectedImage.src,\n          alt: selectedImage.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: selectedImage.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: selectedImage.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"btn btn-primary\",\n            onClick: closeModal,\n            children: \"View Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 9\n  }, this);\n};\n_s(Gallery, \"bpzD2AgrN4HQGY5afK+JzNe/4tI=\");\n_c = Gallery;\nexport default Gallery;\nvar _c;\n$RefreshReg$(_c, \"Gallery\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "jsxDEV", "_jsxDEV", "Gallery", "_s", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedImage", "setSelectedImage", "isLoading", "setIsLoading", "searchTerm", "setSearchTerm", "viewMode", "setViewMode", "galleryRef", "categories", "id", "name", "icon", "count", "galleryImages", "src", "category", "title", "description", "tags", "featured", "size", "filteredImages", "filter", "img", "matchesCategory", "matchesSearch", "toLowerCase", "includes", "some", "tag", "timer", "setTimeout", "clearTimeout", "_galleryRef$current", "observer", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "target", "classList", "add", "threshold", "galleryItems", "current", "querySelectorAll", "item", "observe", "disconnect", "openModal", "image", "document", "body", "style", "overflow", "closeModal", "handleKeyPress", "e", "key", "addEventListener", "removeEventListener", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "length", "cx", "cy", "r", "type", "placeholder", "value", "onChange", "onClick", "x1", "y1", "x2", "y2", "x", "y", "map", "alt", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/Gallery.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Gallery = () => {\n    const [selectedCategory, setSelectedCategory] = useState('all');\n    const [selectedImage, setSelectedImage] = useState(null);\n    const [isLoading, setIsLoading] = useState(true);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'masonry'\n    const galleryRef = useRef(null);\n\n    const categories = [\n        { id: 'all', name: 'All', icon: '🏢', count: 12 },\n        { id: 'desks', name: 'Des<PERSON>', icon: '🖥️', count: 4 },\n        { id: 'chairs', name: 'Chairs', icon: '🪑', count: 3 },\n        { id: 'storage', name: 'Storage', icon: '📚', count: 2 },\n        { id: 'conference', name: 'Conference', icon: '🤝', count: 2 },\n        { id: 'workspace', name: 'Workspace', icon: '💼', count: 1 }\n    ];\n\n    const galleryImages = [\n        {\n            id: 1,\n            src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',\n            category: 'desks',\n            title: 'Executive Mahogany Desk',\n            description: 'Premium executive workspace with rich mahogany finish',\n            tags: ['executive', 'mahogany', 'premium'],\n            featured: true,\n            size: 'large'\n        },\n        {\n            id: 2,\n            src: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=600&h=800&fit=crop',\n            category: 'desks',\n            title: 'Modern Glass Desk',\n            description: 'Contemporary design meets functionality',\n            tags: ['modern', 'glass', 'contemporary'],\n            featured: false,\n            size: 'medium'\n        },\n        {\n            id: 3,\n            src: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop',\n            category: 'chairs',\n            title: 'Ergonomic Executive Chair',\n            description: 'Comfort and style combined for long work sessions',\n            tags: ['ergonomic', 'executive', 'comfort'],\n            featured: true,\n            size: 'medium'\n        },\n        {\n            id: 4,\n            src: 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=600&h=600&fit=crop',\n            category: 'storage',\n            title: 'Executive Filing Cabinet',\n            description: 'Organized storage solutions for modern offices',\n            tags: ['filing', 'storage', 'organization'],\n            featured: false,\n            size: 'small'\n        },\n        {\n            id: 5,\n            src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=500&fit=crop',\n            category: 'conference',\n            title: 'Conference Room Setup',\n            description: 'Professional meeting spaces that inspire collaboration',\n            tags: ['conference', 'meeting', 'collaboration'],\n            featured: true,\n            size: 'large'\n        },\n        {\n            id: 6,\n            src: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=600&h=700&fit=crop',\n            category: 'desks',\n            title: 'Standing Desk Setup',\n            description: 'Health-conscious workspace for active professionals',\n            tags: ['standing', 'health', 'adjustable'],\n            featured: false,\n            size: 'medium'\n        },\n        {\n            id: 7,\n            src: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=600&h=400&fit=crop',\n            category: 'chairs',\n            title: 'Modern Office Chairs',\n            description: 'Contemporary seating solutions for every workspace',\n            tags: ['modern', 'seating', 'contemporary'],\n            featured: false,\n            size: 'medium'\n        },\n        {\n            id: 8,\n            src: 'https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=600&h=800&fit=crop',\n            category: 'storage',\n            title: 'Modern Bookshelf',\n            description: 'Stylish storage and display for office libraries',\n            tags: ['bookshelf', 'display', 'modern'],\n            featured: false,\n            size: 'medium'\n        },\n        {\n            id: 9,\n            src: 'https://images.unsplash.com/photo-1497366412874-3415097a27e7?w=800&h=600&fit=crop',\n            category: 'workspace',\n            title: 'Complete Office Setup',\n            description: 'Fully furnished modern office workspace',\n            tags: ['complete', 'modern', 'workspace'],\n            featured: true,\n            size: 'large'\n        },\n        {\n            id: 10,\n            src: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=600&fit=crop',\n            category: 'chairs',\n            title: 'Mesh Task Chair',\n            description: 'Breathable mesh design for all-day comfort',\n            tags: ['mesh', 'task', 'breathable'],\n            featured: false,\n            size: 'small'\n        },\n        {\n            id: 11,\n            src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=500&fit=crop',\n            category: 'desks',\n            title: 'L-Shaped Executive Desk',\n            description: 'Spacious corner desk for maximum productivity',\n            tags: ['l-shaped', 'corner', 'spacious'],\n            featured: false,\n            size: 'medium'\n        },\n        {\n            id: 12,\n            src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop',\n            category: 'conference',\n            title: 'Boardroom Table',\n            description: 'Impressive boardroom table for executive meetings',\n            tags: ['boardroom', 'executive', 'meetings'],\n            featured: false,\n            size: 'large'\n        }\n    ];\n\n    // Enhanced filtering with search\n    const filteredImages = galleryImages.filter(img => {\n        const matchesCategory = selectedCategory === 'all' || img.category === selectedCategory;\n        const matchesSearch = searchTerm === '' ||\n            img.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n            img.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n            img.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        return matchesCategory && matchesSearch;\n    });\n\n    // Loading simulation\n    useEffect(() => {\n        setIsLoading(true);\n        const timer = setTimeout(() => {\n            setIsLoading(false);\n        }, 800);\n        return () => clearTimeout(timer);\n    }, [selectedCategory, searchTerm]);\n\n    // Intersection Observer for animations\n    useEffect(() => {\n        const observer = new IntersectionObserver(\n            (entries) => {\n                entries.forEach((entry) => {\n                    if (entry.isIntersecting) {\n                        entry.target.classList.add('animate-in');\n                    }\n                });\n            },\n            { threshold: 0.1 }\n        );\n\n        const galleryItems = galleryRef.current?.querySelectorAll('.gallery-item');\n        galleryItems?.forEach((item) => observer.observe(item));\n\n        return () => observer.disconnect();\n    }, [filteredImages]);\n\n    const openModal = (image) => {\n        setSelectedImage(image);\n        document.body.style.overflow = 'hidden';\n    };\n\n    const closeModal = () => {\n        setSelectedImage(null);\n        document.body.style.overflow = 'unset';\n    };\n\n    const handleKeyPress = (e) => {\n        if (e.key === 'Escape') closeModal();\n    };\n\n    useEffect(() => {\n        document.addEventListener('keydown', handleKeyPress);\n        return () => document.removeEventListener('keydown', handleKeyPress);\n    }, []);\n\n    return (\n        <div className=\"gallery-page\">\n            {/* Hero Section */}\n            <section className=\"gallery-hero\">\n                <div className=\"hero-background\">\n                    <div className=\"hero-overlay\"></div>\n                </div>\n                <div className=\"container\">\n                    <div className=\"hero-content\">\n                        <div className=\"breadcrumb\">\n                            <Link to=\"/\">\n                                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <path d=\"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M9 22V12H15V22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                                Home\n                            </Link>\n                            <span>/</span>\n                            <span>Gallery</span>\n                        </div>\n                        <h1 className=\"hero-title\">\n                            <span className=\"title-main\">Design Gallery</span>\n                            <span className=\"title-sub\">Where Inspiration Meets Innovation</span>\n                        </h1>\n                        <p className=\"hero-description\">\n                            Discover our curated collection of premium office furniture showcased in real workspace environments.\n                            From executive suites to collaborative spaces, find the perfect pieces to transform your office.\n                        </p>\n                        <div className=\"hero-stats\">\n                            <div className=\"stat\">\n                                <span className=\"stat-number\">{galleryImages.length}</span>\n                                <span className=\"stat-label\">Showcase Images</span>\n                            </div>\n                            <div className=\"stat\">\n                                <span className=\"stat-number\">{categories.length - 1}</span>\n                                <span className=\"stat-label\">Categories</span>\n                            </div>\n                            <div className=\"stat\">\n                                <span className=\"stat-number\">100+</span>\n                                <span className=\"stat-label\">Products</span>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </section>\n\n            <div className=\"container\">\n                {/* Search and Filters */}\n                <section className=\"gallery-controls\">\n                    <div className=\"controls-header\">\n                        <div className=\"search-container\">\n                            <div className=\"search-input-wrapper\">\n                                <svg className=\"search-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                                <input\n                                    type=\"text\"\n                                    placeholder=\"Search gallery...\"\n                                    value={searchTerm}\n                                    onChange={(e) => setSearchTerm(e.target.value)}\n                                    className=\"search-input\"\n                                />\n                                {searchTerm && (\n                                    <button\n                                        className=\"clear-search\"\n                                        onClick={() => setSearchTerm('')}\n                                    >\n                                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                            <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                            <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                        </svg>\n                                    </button>\n                                )}\n                            </div>\n                        </div>\n\n                        <div className=\"view-controls\">\n                            <button\n                                className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}\n                                onClick={() => setViewMode('grid')}\n                                title=\"Grid View\"\n                            >\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                            </button>\n                            <button\n                                className={`view-btn ${viewMode === 'masonry' ? 'active' : ''}`}\n                                onClick={() => setViewMode('masonry')}\n                                title=\"Masonry View\"\n                            >\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <rect x=\"3\" y=\"3\" width=\"7\" height=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"3\" width=\"7\" height=\"5\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"12\" width=\"7\" height=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"3\" y=\"16\" width=\"7\" height=\"5\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                            </button>\n                        </div>\n                    </div>\n\n                    <div className=\"filter-categories\">\n                        {categories.map(category => (\n                            <button\n                                key={category.id}\n                                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}\n                                onClick={() => setSelectedCategory(category.id)}\n                            >\n                                <span className=\"category-icon\">{category.icon}</span>\n                                <span className=\"category-name\">{category.name}</span>\n                                <span className=\"category-count\">{category.count}</span>\n                            </button>\n                        ))}\n                    </div>\n\n                    <div className=\"results-info\">\n                        <span className=\"results-count\">\n                            {isLoading ? 'Loading...' : `${filteredImages.length} ${filteredImages.length === 1 ? 'image' : 'images'} found`}\n                        </span>\n                        {searchTerm && (\n                            <span className=\"search-term\">for \"{searchTerm}\"</span>\n                        )}\n                    </div>\n                </section>\n\n                <section className=\"gallery-grid\">\n                    {filteredImages.map(image => (\n                        <div \n                            key={image.id} \n                            className=\"gallery-item\"\n                            onClick={() => openModal(image)}\n                        >\n                            <img src={image.src} alt={image.title} />\n                            <div className=\"gallery-overlay\">\n                                <h3>{image.title}</h3>\n                                <p>{image.description}</p>\n                                <button className=\"view-btn\">View Details</button>\n                            </div>\n                        </div>\n                    ))}\n                </section>\n\n                {filteredImages.length === 0 && (\n                    <div className=\"no-results\">\n                        <p>No images found for the selected category.</p>\n                    </div>\n                )}\n\n                <section className=\"gallery-cta\">\n                    <div className=\"cta-content\">\n                        <h2>Inspired by What You See?</h2>\n                        <p>Browse our complete collection and find the perfect pieces for your workspace</p>\n                        <Link to=\"/products\" className=\"btn btn-primary\">\n                            Shop Now\n                        </Link>\n                    </div>\n                </section>\n            </div>\n\n            {/* Modal */}\n            {selectedImage && (\n                <div className=\"modal-overlay\" onClick={closeModal}>\n                    <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n                        <button className=\"modal-close\" onClick={closeModal}>×</button>\n                        <img src={selectedImage.src} alt={selectedImage.title} />\n                        <div className=\"modal-info\">\n                            <h3>{selectedImage.title}</h3>\n                            <p>{selectedImage.description}</p>\n                            <Link \n                                to=\"/products\" \n                                className=\"btn btn-primary\"\n                                onClick={closeModal}\n                            >\n                                View Products\n                            </Link>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default Gallery;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAClD,MAAMkB,UAAU,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMiB,UAAU,GAAG,CACf;IAAEC,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAG,CAAC,EACjD;IAAEH,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAE,CAAC,EACrD;IAAEH,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,EACtD;IAAEH,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,EACxD;IAAEH,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,EAC9D;IAAEH,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,CAC/D;EAED,MAAMC,aAAa,GAAG,CAClB;IACIJ,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,uDAAuD;IACpEC,IAAI,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,CAAC;IAC1CC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,gFAAgF;IACrFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,yCAAyC;IACtDC,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,cAAc,CAAC;IACzCC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,mDAAmD;IAChEC,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC;IAC3CC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,gDAAgD;IAC7DC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,cAAc,CAAC;IAC3CC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,YAAY;IACtBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,wDAAwD;IACrEC,IAAI,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,eAAe,CAAC;IAChDC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,qDAAqD;IAClEC,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,CAAC;IAC1CC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,oDAAoD;IACjEC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,cAAc,CAAC;IAC3CC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,kDAAkD;IAC/DC,IAAI,EAAE,CAAC,WAAW,EAAE,SAAS,EAAE,QAAQ,CAAC;IACxCC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,CAAC;IACLK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,yCAAyC;IACtDC,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC;IACzCC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,EAAE;IACNK,GAAG,EAAE,gFAAgF;IACrFC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,4CAA4C;IACzDC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC;IACpCC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,EAAE;IACNK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,+CAA+C;IAC5DC,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;IACxCC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,EACD;IACIX,EAAE,EAAE,EAAE;IACNK,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,YAAY;IACtBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,mDAAmD;IAChEC,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC;IAC5CC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACV,CAAC,CACJ;;EAED;EACA,MAAMC,cAAc,GAAGR,aAAa,CAACS,MAAM,CAACC,GAAG,IAAI;IAC/C,MAAMC,eAAe,GAAG3B,gBAAgB,KAAK,KAAK,IAAI0B,GAAG,CAACR,QAAQ,KAAKlB,gBAAgB;IACvF,MAAM4B,aAAa,GAAGtB,UAAU,KAAK,EAAE,IACnCoB,GAAG,CAACP,KAAK,CAACU,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAAC,IAC1DH,GAAG,CAACN,WAAW,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAAC,IAChEH,GAAG,CAACL,IAAI,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,CAAC,CAAC,CAAC,CAAC;IAC9E,OAAOF,eAAe,IAAIC,aAAa;EAC3C,CAAC,CAAC;;EAEF;EACAnC,SAAS,CAAC,MAAM;IACZY,YAAY,CAAC,IAAI,CAAC;IAClB,MAAM4B,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC3B7B,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC,EAAE,GAAG,CAAC;IACP,OAAO,MAAM8B,YAAY,CAACF,KAAK,CAAC;EACpC,CAAC,EAAE,CAACjC,gBAAgB,EAAEM,UAAU,CAAC,CAAC;;EAElC;EACAb,SAAS,CAAC,MAAM;IAAA,IAAA2C,mBAAA;IACZ,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CACpCC,OAAO,IAAK;MACTA,OAAO,CAACC,OAAO,CAAEC,KAAK,IAAK;QACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACtBD,KAAK,CAACE,MAAM,CAACC,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN,CAAC,EACD;MAAEC,SAAS,EAAE;IAAI,CACrB,CAAC;IAED,MAAMC,YAAY,IAAAX,mBAAA,GAAG1B,UAAU,CAACsC,OAAO,cAAAZ,mBAAA,uBAAlBA,mBAAA,CAAoBa,gBAAgB,CAAC,eAAe,CAAC;IAC1EF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEP,OAAO,CAAEU,IAAI,IAAKb,QAAQ,CAACc,OAAO,CAACD,IAAI,CAAC,CAAC;IAEvD,OAAO,MAAMb,QAAQ,CAACe,UAAU,CAAC,CAAC;EACtC,CAAC,EAAE,CAAC5B,cAAc,CAAC,CAAC;EAEpB,MAAM6B,SAAS,GAAIC,KAAK,IAAK;IACzBnD,gBAAgB,CAACmD,KAAK,CAAC;IACvBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EAC3C,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrBxD,gBAAgB,CAAC,IAAI,CAAC;IACtBoD,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;EAC1C,CAAC;EAED,MAAME,cAAc,GAAIC,CAAC,IAAK;IAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAEH,UAAU,CAAC,CAAC;EACxC,CAAC;EAEDlE,SAAS,CAAC,MAAM;IACZ8D,QAAQ,CAACQ,gBAAgB,CAAC,SAAS,EAAEH,cAAc,CAAC;IACpD,OAAO,MAAML,QAAQ,CAACS,mBAAmB,CAAC,SAAS,EAAEJ,cAAc,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACI/D,OAAA;IAAKoE,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAEzBrE,OAAA;MAASoE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC7BrE,OAAA;QAAKoE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BrE,OAAA;UAAKoE,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACNzE,OAAA;QAAKoE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBrE,OAAA;UAAKoE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBrE,OAAA;YAAKoE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBrE,OAAA,CAACF,IAAI;cAAC4E,EAAE,EAAC,GAAG;cAAAL,QAAA,gBACRrE,OAAA;gBAAK2E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvDrE,OAAA;kBAAM+E,CAAC,EAAC,8KAA8K;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9NzE,OAAA;kBAAM+E,CAAC,EAAC,gBAAgB;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,QAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPzE,OAAA;cAAAqE,QAAA,EAAM;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACdzE,OAAA;cAAAqE,QAAA,EAAM;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNzE,OAAA;YAAIoE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACtBrE,OAAA;cAAMoE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDzE,OAAA;cAAMoE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACLzE,OAAA;YAAGoE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAGhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJzE,OAAA;YAAKoE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACvBrE,OAAA;cAAKoE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACjBrE,OAAA;gBAAMoE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAElD,aAAa,CAAC+D;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DzE,OAAA;gBAAMoE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNzE,OAAA;cAAKoE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACjBrE,OAAA;gBAAMoE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEvD,UAAU,CAACoE,MAAM,GAAG;cAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5DzE,OAAA;gBAAMoE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNzE,OAAA;cAAKoE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACjBrE,OAAA;gBAAMoE,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzCzE,OAAA;gBAAMoE,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEVzE,OAAA;MAAKoE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAEtBrE,OAAA;QAASoE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACjCrE,OAAA;UAAKoE,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BrE,OAAA;YAAKoE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7BrE,OAAA;cAAKoE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjCrE,OAAA;gBAAKoE,SAAS,EAAC,aAAa;gBAACO,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBAC/ErE,OAAA;kBAAQmF,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,GAAG;kBAACL,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACrEzE,OAAA;kBAAM+E,CAAC,EAAC,oBAAoB;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNzE,OAAA;gBACIsF,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,mBAAmB;gBAC/BC,KAAK,EAAE/E,UAAW;gBAClBgF,QAAQ,EAAGzB,CAAC,IAAKtD,aAAa,CAACsD,CAAC,CAAClB,MAAM,CAAC0C,KAAK,CAAE;gBAC/CpB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,EACDhE,UAAU,iBACPT,OAAA;gBACIoE,SAAS,EAAC,cAAc;gBACxBsB,OAAO,EAAEA,CAAA,KAAMhF,aAAa,CAAC,EAAE,CAAE;gBAAA2D,QAAA,eAEjCrE,OAAA;kBAAK2E,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAT,QAAA,gBACvDrE,OAAA;oBAAM2F,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,IAAI;oBAACd,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC3EzE,OAAA;oBAAM2F,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACd,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENzE,OAAA;YAAKoE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BrE,OAAA;cACIoE,SAAS,EAAE,YAAYzD,QAAQ,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC7D+E,OAAO,EAAEA,CAAA,KAAM9E,WAAW,CAAC,MAAM,CAAE;cACnCU,KAAK,EAAC,WAAW;cAAA+C,QAAA,eAEjBrE,OAAA;gBAAK2E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvDrE,OAAA;kBAAM+F,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9EzE,OAAA;kBAAM+F,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,GAAG;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/EzE,OAAA;kBAAM+F,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChFzE,OAAA;kBAAM+F,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,IAAI;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACTzE,OAAA;cACIoE,SAAS,EAAE,YAAYzD,QAAQ,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;cAChE+E,OAAO,EAAEA,CAAA,KAAM9E,WAAW,CAAC,SAAS,CAAE;cACtCU,KAAK,EAAC,cAAc;cAAA+C,QAAA,eAEpBrE,OAAA;gBAAK2E,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvDrE,OAAA;kBAAM+F,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9EzE,OAAA;kBAAM+F,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,GAAG;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/EzE,OAAA;kBAAM+F,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChFzE,OAAA;kBAAM+F,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,IAAI;kBAACrB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACI,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENzE,OAAA;UAAKoE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC7BvD,UAAU,CAACmF,GAAG,CAAC5E,QAAQ,iBACpBrB,OAAA;YAEIoE,SAAS,EAAE,gBAAgBjE,gBAAgB,KAAKkB,QAAQ,CAACN,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC9E2E,OAAO,EAAEA,CAAA,KAAMtF,mBAAmB,CAACiB,QAAQ,CAACN,EAAE,CAAE;YAAAsD,QAAA,gBAEhDrE,OAAA;cAAMoE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEhD,QAAQ,CAACJ;YAAI;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDzE,OAAA;cAAMoE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEhD,QAAQ,CAACL;YAAI;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDzE,OAAA;cAAMoE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAEhD,QAAQ,CAACH;YAAK;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GANnDpD,QAAQ,CAACN,EAAE;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOZ,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENzE,OAAA;UAAKoE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBrE,OAAA;YAAMoE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC1B9D,SAAS,GAAG,YAAY,GAAG,GAAGoB,cAAc,CAACuD,MAAM,IAAIvD,cAAc,CAACuD,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,QAAQ;UAAQ;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G,CAAC,EACNhE,UAAU,iBACPT,OAAA;YAAMoE,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,QAAK,EAAC5D,UAAU,EAAC,IAAC;UAAA;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEVzE,OAAA;QAASoE,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC5B1C,cAAc,CAACsE,GAAG,CAACxC,KAAK,iBACrBzD,OAAA;UAEIoE,SAAS,EAAC,cAAc;UACxBsB,OAAO,EAAEA,CAAA,KAAMlC,SAAS,CAACC,KAAK,CAAE;UAAAY,QAAA,gBAEhCrE,OAAA;YAAKoB,GAAG,EAAEqC,KAAK,CAACrC,GAAI;YAAC8E,GAAG,EAAEzC,KAAK,CAACnC;UAAM;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCzE,OAAA;YAAKoE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BrE,OAAA;cAAAqE,QAAA,EAAKZ,KAAK,CAACnC;YAAK;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtBzE,OAAA;cAAAqE,QAAA,EAAIZ,KAAK,CAAClC;YAAW;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BzE,OAAA;cAAQoE,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA,GATDhB,KAAK,CAAC1C,EAAE;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUZ,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,EAET9C,cAAc,CAACuD,MAAM,KAAK,CAAC,iBACxBlF,OAAA;QAAKoE,SAAS,EAAC,YAAY;QAAAC,QAAA,eACvBrE,OAAA;UAAAqE,QAAA,EAAG;QAA0C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CACR,eAEDzE,OAAA;QAASoE,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC5BrE,OAAA;UAAKoE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBrE,OAAA;YAAAqE,QAAA,EAAI;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClCzE,OAAA;YAAAqE,QAAA,EAAG;UAA6E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpFzE,OAAA,CAACF,IAAI;YAAC4E,EAAE,EAAC,WAAW;YAACN,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAEjD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,EAGLpE,aAAa,iBACVL,OAAA;MAAKoE,SAAS,EAAC,eAAe;MAACsB,OAAO,EAAE5B,UAAW;MAAAO,QAAA,eAC/CrE,OAAA;QAAKoE,SAAS,EAAC,eAAe;QAACsB,OAAO,EAAG1B,CAAC,IAAKA,CAAC,CAACmC,eAAe,CAAC,CAAE;QAAA9B,QAAA,gBAC/DrE,OAAA;UAAQoE,SAAS,EAAC,aAAa;UAACsB,OAAO,EAAE5B,UAAW;UAAAO,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC/DzE,OAAA;UAAKoB,GAAG,EAAEf,aAAa,CAACe,GAAI;UAAC8E,GAAG,EAAE7F,aAAa,CAACiB;QAAM;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDzE,OAAA;UAAKoE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBrE,OAAA;YAAAqE,QAAA,EAAKhE,aAAa,CAACiB;UAAK;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9BzE,OAAA;YAAAqE,QAAA,EAAIhE,aAAa,CAACkB;UAAW;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClCzE,OAAA,CAACF,IAAI;YACD4E,EAAE,EAAC,WAAW;YACdN,SAAS,EAAC,iBAAiB;YAC3BsB,OAAO,EAAE5B,UAAW;YAAAO,QAAA,EACvB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACvE,EAAA,CA9XID,OAAO;AAAAmG,EAAA,GAAPnG,OAAO;AAgYb,eAAeA,OAAO;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}