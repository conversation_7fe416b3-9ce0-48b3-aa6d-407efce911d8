import React, { Suspense, useState, useRef } from 'react';
import { Canvas } from '@react-three/fiber';
import {
  OrbitControls,
  Environment,
  ContactShadows,
  Html
} from '@react-three/drei';
import * as THREE from 'three';
import '../styles/configurator.css';

// S-407 Chair Model Component (Procedural Fallback)
function S407ChairModel({
  scale = 1,
  position = [0, 0, 0],
  rotation = [0, 0, 0],
  color = '#8B4513',
  material = 'wood',
  ...props
}) {
  const chairRef = useRef();

  // Create procedural chair geometry
  const createChairMaterial = () => {
    const materialProps = {
      color: new THREE.Color(color),
    };

    switch (material) {
      case 'wood':
        return { ...materialProps, roughness: 0.8, metalness: 0.0 };
      case 'metal':
        return { ...materialProps, roughness: 0.2, metalness: 0.9, color: new THREE.Color('#C0C0C0') };
      case 'leather':
        return { ...materialProps, roughness: 0.6, metalness: 0.0 };
      case 'fabric':
        return { ...materialProps, roughness: 0.9, metalness: 0.0 };
      default:
        return { ...materialProps, roughness: 0.7, metalness: 0.0 };
    }
  };

  const materialProps = createChairMaterial();

  return (
    <group ref={chairRef} scale={scale} position={position} rotation={rotation} {...props}>
      {/* Seat */}
      <mesh position={[0, 0.4, 0]} castShadow receiveShadow>
        <boxGeometry args={[0.5, 0.06, 0.5]} />
        <meshStandardMaterial {...materialProps} />
      </mesh>

      {/* Backrest */}
      <mesh position={[0, 0.7, -0.22]} castShadow receiveShadow>
        <boxGeometry args={[0.5, 0.6, 0.06]} />
        <meshStandardMaterial {...materialProps} />
      </mesh>

      {/* Base */}
      <mesh position={[0, 0.02, 0]} castShadow receiveShadow>
        <cylinderGeometry args={[0.25, 0.25, 0.04, 8]} />
        <meshStandardMaterial {...materialProps} />
      </mesh>

      {/* Legs */}
      {[
        [-0.2, 0.2, -0.2],
        [0.2, 0.2, -0.2],
        [-0.2, 0.2, 0.2],
        [0.2, 0.2, 0.2],
        [0, 0.2, 0]
      ].map((pos, index) => (
        <mesh key={index} position={pos} castShadow>
          <cylinderGeometry args={[0.02, 0.02, 0.4, 6]} />
          <meshStandardMaterial {...materialProps} />
        </mesh>
      ))}

      {/* Armrests */}
      <mesh position={[-0.3, 0.6, 0]} castShadow receiveShadow>
        <boxGeometry args={[0.06, 0.06, 0.3]} />
        <meshStandardMaterial {...materialProps} />
      </mesh>
      <mesh position={[0.3, 0.6, 0]} castShadow receiveShadow>
        <boxGeometry args={[0.06, 0.06, 0.3]} />
        <meshStandardMaterial {...materialProps} />
      </mesh>
    </group>
  );
}

// Loading component
function LoadingSpinner() {
  return (
    <Html center>
      <div className="loading-spinner-3d">
        <div className="spinner"></div>
        <p>Loading S-407 Chair...</p>
      </div>
    </Html>
  );
}



// Main S-407 Chair Configurator Component
const S407ChairConfigurator = ({ onBack, product }) => {
  // Configuration state
  const [chairConfig, setChairConfig] = useState({
    color: '#8B4513',
    material: 'wood',
    scale: 1,
    quantity: 1
  });

  // View presets
  const [currentView, setCurrentView] = useState('default');
  const controlsRef = useRef();

  // Material options
  const materialOptions = [
    { value: 'wood', label: 'Wood', color: '#8B4513' },
    { value: 'metal', label: 'Metal', color: '#C0C0C0' },
    { value: 'leather', label: 'Leather', color: '#654321' },
    { value: 'fabric', label: 'Fabric', color: '#4A4A4A' }
  ];

  // Color presets
  const colorPresets = [
    '#8B4513', // Brown
    '#654321', // Dark Brown
    '#2F4F4F', // Dark Slate Gray
    '#000000', // Black
    '#FFFFFF', // White
    '#C0C0C0', // Silver
    '#800000', // Maroon
    '#000080'  // Navy
  ];

  // View presets
  const viewPresets = [
    { name: 'Front', position: [0, 0, 3], target: [0, 0, 0] },
    { name: 'Side', position: [3, 0, 0], target: [0, 0, 0] },
    { name: 'Back', position: [0, 0, -3], target: [0, 0, 0] },
    { name: 'Top', position: [0, 3, 0], target: [0, 0, 0] },
    { name: 'Isometric', position: [2, 2, 2], target: [0, 0, 0] }
  ];

  // Handle material change
  const handleMaterialChange = (newMaterial) => {
    const materialData = materialOptions.find(m => m.value === newMaterial);
    setChairConfig(prev => ({
      ...prev,
      material: newMaterial,
      color: materialData?.color || prev.color
    }));
  };

  // Handle view change
  const handleViewChange = (viewName) => {
    const view = viewPresets.find(v => v.name === viewName);
    if (view && controlsRef.current) {
      controlsRef.current.setLookAt(
        view.position[0], view.position[1], view.position[2],
        view.target[0], view.target[1], view.target[2],
        true
      );
      setCurrentView(viewName);
    }
  };

  // Calculate price
  const calculatePrice = () => {
    const basePrice = product?.price || 299.99;
    const materialMultiplier = {
      wood: 1.0,
      metal: 1.2,
      leather: 1.8,
      fabric: 0.9
    };
    
    return (basePrice * (materialMultiplier[chairConfig.material] || 1.0) * chairConfig.quantity).toFixed(2);
  };

  return (
    <div className="configurator-container">
      {/* Header */}
      <div className="configurator-header">
        <button onClick={onBack} className="back-button">
          ← Back to Product
        </button>
        <h2>S-407 Chair Configurator</h2>
        <div className="price-display">
          ${calculatePrice()}
        </div>
      </div>

      {/* Main Content */}
      <div className="configurator-main">
        <div className="container">
          <div className="configurator-layout-horizontal">
            
            {/* 3D Viewer Panel */}
            <div className="viewer-panel">
              <div className="config-card viewer-card">
                <div className="card-header">
                  <div className="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="#F0B21B"/>
                      <path d="M2 17L12 22L22 17" stroke="#F0B21B" strokeWidth="2"/>
                      <path d="M2 12L12 17L22 12" stroke="#F0B21B" strokeWidth="2"/>
                    </svg>
                  </div>
                  <div className="card-title">
                    <h4>S-407 Chair Preview</h4>
                    <p>Interactive 3D model with real-time customization</p>
                  </div>
                </div>
                
                <div className="model-viewer-container">
                  <Canvas
                    camera={{ position: [2, 2, 2], fov: 50 }}
                    style={{ height: '400px', background: '#F0B21B' }}
                    shadows
                  >
                    <Suspense fallback={<LoadingSpinner />}>
                      {/* Lighting */}
                      <ambientLight intensity={0.4} />
                      <directionalLight 
                        position={[10, 10, 5]} 
                        intensity={1}
                        castShadow
                        shadow-mapSize-width={2048}
                        shadow-mapSize-height={2048}
                      />
                      
                      {/* Environment */}
                      <Environment preset="studio" />
                      
                      {/* Chair Model */}
                      <S407ChairModel
                        scale={chairConfig.scale}
                        color={chairConfig.color}
                        material={chairConfig.material}
                        position={[0, -0.5, 0]}
                      />
                      
                      {/* Ground/Shadows */}
                      <ContactShadows 
                        position={[0, -0.8, 0]} 
                        opacity={0.4} 
                        scale={3} 
                        blur={2.5} 
                        far={4} 
                      />
                      
                      {/* Controls */}
                      <OrbitControls 
                        ref={controlsRef}
                        enablePan={true}
                        enableZoom={true}
                        enableRotate={true}
                        minDistance={1}
                        maxDistance={8}
                        target={[0, 0, 0]}
                      />
                    </Suspense>
                  </Canvas>
                </div>

                {/* View Controls */}
                <div className="view-controls">
                  <h5>View Presets</h5>
                  <div className="view-buttons">
                    {viewPresets.map((view) => (
                      <button
                        key={view.name}
                        className={`view-btn ${currentView === view.name ? 'active' : ''}`}
                        onClick={() => handleViewChange(view.name)}
                      >
                        {view.name}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Configuration Panel */}
            <div className="config-panel">
              <div className="config-card">
                <div className="card-header">
                  <div className="card-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" fill="#F0B21B"/>
                      <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="card-title">
                    <h4>Chair Configuration</h4>
                    <p>Customize your S-407 chair</p>
                  </div>
                </div>

                <div className="config-content">
                  {/* Material Selection */}
                  <div className="config-section">
                    <h5>Material</h5>
                    <div className="material-grid">
                      {materialOptions.map((mat) => (
                        <button
                          key={mat.value}
                          className={`material-btn ${chairConfig.material === mat.value ? 'active' : ''}`}
                          onClick={() => handleMaterialChange(mat.value)}
                        >
                          <div 
                            className="material-preview" 
                            style={{ backgroundColor: mat.color }}
                          ></div>
                          <span>{mat.label}</span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Color Selection */}
                  <div className="config-section">
                    <h5>Color</h5>
                    <div className="color-grid">
                      {colorPresets.map((color) => (
                        <button
                          key={color}
                          className={`color-btn ${chairConfig.color === color ? 'active' : ''}`}
                          style={{ backgroundColor: color }}
                          onClick={() => setChairConfig(prev => ({ ...prev, color }))}
                        />
                      ))}
                    </div>
                    <input
                      type="color"
                      value={chairConfig.color}
                      onChange={(e) => setChairConfig(prev => ({ ...prev, color: e.target.value }))}
                      className="color-picker"
                    />
                  </div>

                  {/* Scale */}
                  <div className="config-section">
                    <h5>Scale: {chairConfig.scale.toFixed(1)}x</h5>
                    <input
                      type="range"
                      min="0.5"
                      max="2"
                      step="0.1"
                      value={chairConfig.scale}
                      onChange={(e) => setChairConfig(prev => ({ ...prev, scale: parseFloat(e.target.value) }))}
                      className="scale-slider"
                    />
                  </div>

                  {/* Quantity */}
                  <div className="config-section">
                    <h5>Quantity</h5>
                    <div className="quantity-controls">
                      <button 
                        onClick={() => setChairConfig(prev => ({ ...prev, quantity: Math.max(1, prev.quantity - 1) }))}
                        className="quantity-btn"
                      >
                        -
                      </button>
                      <span className="quantity-display">{chairConfig.quantity}</span>
                      <button 
                        onClick={() => setChairConfig(prev => ({ ...prev, quantity: prev.quantity + 1 }))}
                        className="quantity-btn"
                      >
                        +
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Summary */}
              <div className="config-card order-summary">
                <div className="card-header">
                  <h4>Order Summary</h4>
                </div>
                <div className="summary-content">
                  <div className="summary-item">
                    <span>S-407 Chair ({chairConfig.material})</span>
                    <span>${calculatePrice()}</span>
                  </div>
                  <div className="summary-item">
                    <span>Quantity</span>
                    <span>{chairConfig.quantity}</span>
                  </div>
                  <div className="summary-total">
                    <span>Total</span>
                    <span>${calculatePrice()}</span>
                  </div>
                  <button 
                    className="btn btn-primary btn-large add-to-cart-btn"
                    onClick={() => alert(`Added ${chairConfig.quantity} S-407 Chair(s) to cart for $${calculatePrice()}`)}
                  >
                    Add to Cart
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default S407ChairConfigurator;
