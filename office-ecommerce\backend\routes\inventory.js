const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { requirePermission, requireStaff } = require('../middleware/rbac');
const {
    getAllInventory,
    getInventoryById,
    createInventory,
    updateInventory,
    getStockMovements,
    getInventoryDashboard
} = require('../controllers/inventoryController');

// All inventory routes require authentication and staff role
router.use(authenticateToken);
router.use(requireStaff);

// GET /api/inventory/dashboard - Get inventory dashboard data
router.get('/dashboard', requirePermission('inventory.read'), getInventoryDashboard);

// GET /api/inventory - Get all inventory items with filtering
router.get('/', requirePermission('inventory.read'), getAllInventory);

// GET /api/inventory/:id - Get specific inventory item
router.get('/:id', requirePermission('inventory.read'), getInventoryById);

// POST /api/inventory - Create new inventory item
router.post('/', requirePermission('inventory.create'), createInventory);

// PUT /api/inventory/:id - Update inventory item
router.put('/:id', requirePermission('inventory.update'), updateInventory);

// GET /api/inventory/:id/movements - Get stock movements for inventory item
router.get('/:id/movements', requirePermission('inventory.read'), getStockMovements);

module.exports = router;
