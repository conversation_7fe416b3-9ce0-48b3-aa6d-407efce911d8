/* Simple Gallery Styles */
.gallery-page {
  min-height: 100vh;
  background: #ffffff;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Header */
.gallery-header {
  padding: 3rem 0 2rem 0;
  text-align: center;
}

.breadcrumb {
  margin-bottom: 1rem;
  color: #666;
  font-size: 0.9rem;
}

.breadcrumb a {
  color: #F0B21B;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.gallery-header h1 {
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 0.5rem;
  color: #333;
}

.gallery-header p {
  color: #666;
  font-size: 1.1rem;
}

/* Controls */
.gallery-controls {
  padding: 2rem 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 3rem;
}

.filter-tabs {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 0.75rem 1.5rem;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  border-radius: 25px;
}

.filter-tab:hover {
  border-color: #F0B21B;
  border-radius: 25px;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(240, 178, 27, 0.2);
}

.filter-tab.active {
  background: #F0B21B;
  color: white;
  border-color: #F0B21B;
  border-radius: 25px;
}

/* Gallery Grid */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.gallery-item {
  position: relative;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.gallery-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.gallery-item img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
  color: white;
  padding: 2rem 1rem 1rem 1rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.gallery-item:hover .image-overlay {
  opacity: 1;
}

.image-overlay h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 3rem;
  color: #666;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 800px;
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0,0,0,0.5);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  z-index: 10;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: rgba(0,0,0,0.7);
}

.modal-content img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  display: block;
}

.modal-info {
  padding: 2rem;
  text-align: center;
}

.modal-info h3 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #333;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: #F0B21B;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s ease;
}

.btn:hover {
  background: #d4a017;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .gallery-header h1 {
    font-size: 2rem;
  }

  .filter-tabs {
    gap: 0.25rem;
  }

  .filter-tab {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .gallery-item img {
    height: 200px;
  }

  .modal-content {
    margin: 1rem;
    max-height: 95vh;
  }

  .modal-content img {
    height: 250px;
  }

  .modal-info {
    padding: 1rem;
  }
}