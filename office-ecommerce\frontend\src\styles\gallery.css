/* Modern Gallery Styles */
.gallery-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Hero Section */
.gallery-hero {
  position: relative;
  height: 60vh;
  min-height: 500px;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('https://images.unsplash.com/photo-1497366216548-37526070297c?w=1920&h=1080&fit=crop') center/cover;
  z-index: 1;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.6) 100%);
  z-index: 2;
}

.hero-content {
  position: relative;
  z-index: 3;
  color: white;
  max-width: 800px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.breadcrumb a {
  color: #F0B21B;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: opacity 0.2s ease;
}

.breadcrumb a:hover {
  opacity: 0.8;
}

.breadcrumb span {
  color: rgba(255,255,255,0.7);
}

.hero-title {
  margin-bottom: 1.5rem;
}

.title-main {
  display: block;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #F0B21B 0%, #FFD700 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-sub {
  display: block;
  font-size: 1.5rem;
  font-weight: 300;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
}

.hero-stats {
  display: flex;
  gap: 3rem;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #F0B21B;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 0.5rem;
}

/* Controls Section */
.gallery-controls {
  padding: 3rem 0;
  background: white;
  border-radius: 20px;
  margin: -60px 0 3rem 0;
  position: relative;
  z-index: 10;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 2rem;
}

.search-container {
  flex: 1;
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: #6c757d;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e9ecef;
  border-radius: 50px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.search-input:focus {
  outline: none;
  border-color: #F0B21B;
  background: white;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.clear-search {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: #f8f9fa;
  color: #dc3545;
}

.view-controls {
  display: flex;
  gap: 0.5rem;
  background: #f8f9fa;
  padding: 0.25rem;
  border-radius: 50px;
}

.view-btn {
  padding: 0.75rem;
  border: none;
  background: transparent;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6c757d;
}

.view-btn:hover {
  background: white;
  color: #F0B21B;
}

.view-btn.active {
  background: #F0B21B;
  color: white;
  box-shadow: 0 2px 8px rgba(240, 178, 27, 0.3);
}

/* Filter Categories */
.filter-categories {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border: 2px solid #e9ecef;
  background: white;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.category-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(240, 178, 27, 0.1), transparent);
  transition: left 0.5s ease;
}

.category-btn:hover::before {
  left: 100%;
}

.category-btn:hover {
  border-color: #F0B21B;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.2);
}

.category-btn.active {
  background: #F0B21B;
  border-color: #F0B21B;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}

.category-icon {
  font-size: 1.2rem;
}

.category-name {
  font-size: 1rem;
}

.category-count {
  background: rgba(255,255,255,0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.category-btn.active .category-count {
  background: rgba(255,255,255,0.3);
}

.results-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.9rem;
}

.search-term {
  color: #F0B21B;
  font-weight: 600;
}

/* Gallery Grid */
.gallery-grid {
  display: grid;
  gap: 2rem;
  margin-bottom: 4rem;
}

.gallery-grid.grid {
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.gallery-grid.masonry {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-auto-rows: 20px;
}

.gallery-item {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  background: white;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  opacity: 0;
  transform: translateY(30px);
}

.gallery-item.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.gallery-grid.grid .gallery-item {
  aspect-ratio: 4/3;
}

.gallery-grid.masonry .gallery-item.small {
  grid-row-end: span 15;
}

.gallery-grid.masonry .gallery-item.medium {
  grid-row-end: span 20;
}

.gallery-grid.masonry .gallery-item.large {
  grid-row-end: span 25;
}

.gallery-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.gallery-item.featured {
  border: 3px solid #F0B21B;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.featured-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: #F0B21B;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(240, 178, 27, 0.3);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, transparent 60%);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-end;
  padding: 2rem;
  color: white;
}

.gallery-item:hover .image-overlay {
  opacity: 1;
}

.overlay-content {
  width: 100%;
}

.image-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.image-description {
  font-size: 1rem;
  opacity: 0.9;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.image-tags {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.tag {
  background: rgba(255,255,255,0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  backdrop-filter: blur(10px);
}

.overlay-actions {
  display: flex;
  gap: 1rem;
}

.action-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.action-btn.primary {
  background: #F0B21B;
  color: white;
}

.action-btn.primary:hover {
  background: #d4a017;
  transform: translateY(-2px);
}

.action-btn.secondary {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  backdrop-filter: blur(10px);
}

.action-btn.secondary:hover {
  background: rgba(255,255,255,0.3);
}

/* Loading Skeleton */
.gallery-item.skeleton {
  opacity: 1;
  transform: none;
  cursor: default;
}

.skeleton-image {
  width: 100%;
  height: 70%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-content {
  padding: 1rem;
}

.skeleton-title {
  height: 1.5rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.skeleton-description {
  height: 1rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  width: 80%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* No Results */
.no-results {
  grid-column: 1 / -1;
  text-align: center;
  padding: 4rem 2rem;
  color: #6c757d;
}

.no-results-icon {
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.no-results h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #495057;
}

.no-results p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* CTA Section */
.gallery-cta {
  position: relative;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 20px;
  overflow: hidden;
  margin: 4rem 0;
}

.cta-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.cta-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23F0B21B" fill-opacity="0.05"><circle cx="30" cy="30" r="2"/></g></svg>') repeat;
}

.cta-content {
  position: relative;
  z-index: 2;
  padding: 4rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 3rem;
  color: white;
}

.cta-text {
  flex: 1;
  max-width: 600px;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #F0B21B 0%, #FFD700 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cta-content p {
  font-size: 1.2rem;
  line-height: 1.6;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.cta-features {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #F0B21B;
  font-weight: 500;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  min-width: 200px;
  justify-content: center;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 900px;
  max-height: 90vh;
  width: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0,0,0,0.5);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.modal-close:hover {
  background: rgba(0,0,0,0.7);
  transform: scale(1.1);
}

.modal-image {
  flex: 1;
  position: relative;
  min-height: 400px;
}

.modal-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modal-featured-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: #F0B21B;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 8px rgba(240, 178, 27, 0.3);
}

.modal-info {
  flex: 1;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 350px;
}

.modal-header {
  margin-bottom: 1rem;
}

.modal-info h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1a1a1a;
}

.modal-category {
  background: #F0B21B;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
}

.modal-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #6c757d;
  margin-bottom: 1.5rem;
}

.modal-tags {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.modal-tag {
  background: #f8f9fa;
  color: #6c757d;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  border: 1px solid #e9ecef;
}

.modal-actions {
  display: flex;
  gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .title-main {
    font-size: 2.5rem;
  }
  
  .title-sub {
    font-size: 1.2rem;
  }
  
  .hero-stats {
    gap: 2rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .controls-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-container {
    max-width: none;
  }
  
  .filter-categories {
    justify-content: center;
  }
  
  .category-btn {
    padding: 0.75rem 1rem;
  }
  
  .gallery-grid.grid {
    grid-template-columns: 1fr;
  }
  
  .gallery-grid.masonry {
    grid-template-columns: 1fr;
  }
  
  .cta-content {
    flex-direction: column;
    text-align: center;
    padding: 2rem;
  }
  
  .cta-content h2 {
    font-size: 2rem;
  }
  
  .cta-features {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .modal-content {
    flex-direction: column;
    max-height: 95vh;
  }
  
  .modal-image {
    min-height: 250px;
  }
  
  .modal-info {
    min-width: auto;
  }
  
  .modal-actions {
    flex-direction: column;
  }
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  line-height: 1;
  text-align: center;
  justify-content: center;
}

.btn-primary {
  background: #F0B21B;
  color: white;
  border: 2px solid #F0B21B;
}

.btn-primary:hover {
  background: #d4a017;
  border-color: #d4a017;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}

.btn-outline {
  background: transparent;
  color: #F0B21B;
  border: 2px solid #F0B21B;
}

.btn-outline:hover {
  background: #F0B21B;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  min-width: 200px;
}

@media (max-width: 480px) {
  .gallery-controls {
    margin: -40px 1rem 2rem 1rem;
    padding: 2rem 1rem;
  }

  .hero-content {
    padding: 0 1rem;
  }

  .title-main {
    font-size: 2rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-categories {
    gap: 0.5rem;
  }

  .category-btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .gallery-grid {
    gap: 1rem;
  }

  .modal-overlay {
    padding: 1rem;
  }

  .modal-info {
    padding: 1.5rem;
  }

  .btn-large {
    min-width: auto;
    width: 100%;
  }
}
