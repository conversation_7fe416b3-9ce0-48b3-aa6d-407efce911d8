USE OfficeEcommerce;
GO

-- Create Roles table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Roles' AND xtype='U')
BEGIN
    CREATE TABLE Roles (
        id INT PRIMARY KEY IDENTITY(1,1),
        name NVARCHAR(50) NOT NULL UNIQUE,
        description NVARCHAR(255) NULL,
        isActive BIT DEFAULT 1,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE()
    );

    -- Insert default roles
    INSERT INTO Roles (name, description) VALUES
    ('Admin', 'Full system access with all permissions'),
    ('Employee', 'Backend access with limited permissions for inventory and order management'),
    ('Customer', 'Frontend access only for shopping and order tracking');

    PRINT 'Roles table created successfully';
END
ELSE
BEGIN
    PRINT 'Roles table already exists';
END
GO

-- Create Permissions table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Permissions' AND xtype='U')
BEGIN
    CREATE TABLE Permissions (
        id INT PRIMARY KEY IDENTITY(1,1),
        name NVARCHAR(100) NOT NULL UNIQUE,
        description NVARCHAR(255) NULL,
        module NVARCHAR(50) NOT NULL, -- e.g., 'inventory', 'orders', 'users', 'analytics'
        action NVARCHAR(50) NOT NULL, -- e.g., 'create', 'read', 'update', 'delete'
        createdAt DATETIME DEFAULT GETDATE()
    );

    -- Insert default permissions
    INSERT INTO Permissions (name, description, module, action) VALUES
    -- User Management
    ('users.create', 'Create new users', 'users', 'create'),
    ('users.read', 'View user information', 'users', 'read'),
    ('users.update', 'Update user information', 'users', 'update'),
    ('users.delete', 'Delete users', 'users', 'delete'),

    -- Product Management
    ('products.create', 'Create new products', 'products', 'create'),
    ('products.read', 'View product information', 'products', 'read'),
    ('products.update', 'Update product information', 'products', 'update'),
    ('products.delete', 'Delete products', 'products', 'delete'),

    -- Inventory Management
    ('inventory.create', 'Add inventory items', 'inventory', 'create'),
    ('inventory.read', 'View inventory levels', 'inventory', 'read'),
    ('inventory.update', 'Update inventory levels', 'inventory', 'update'),
    ('inventory.delete', 'Remove inventory items', 'inventory', 'delete'),

    -- Order Management
    ('orders.create', 'Create new orders', 'orders', 'create'),
    ('orders.read', 'View order information', 'orders', 'read'),
    ('orders.update', 'Update order status', 'orders', 'update'),
    ('orders.delete', 'Cancel/delete orders', 'orders', 'delete'),

    -- Analytics
    ('analytics.read', 'View analytics and reports', 'analytics', 'read'),
    ('analytics.export', 'Export analytics data', 'analytics', 'export'),

    -- System Administration
    ('system.admin', 'Full system administration', 'system', 'admin'),
    ('system.backup', 'Backup system data', 'system', 'backup'),
    ('system.restore', 'Restore system data', 'system', 'restore');

    PRINT 'Permissions table created successfully';
END
ELSE
BEGIN
    PRINT 'Permissions table already exists';
END
GO

-- Create UserRoles junction table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserRoles' AND xtype='U')
BEGIN
    CREATE TABLE UserRoles (
        id INT PRIMARY KEY IDENTITY(1,1),
        userId INT NOT NULL,
        roleId INT NOT NULL,
        assignedAt DATETIME DEFAULT GETDATE(),
        assignedBy INT NULL, -- User who assigned this role
        isActive BIT DEFAULT 1,

        CONSTRAINT FK_UserRoles_Users FOREIGN KEY (userId) REFERENCES Users(id) ON DELETE CASCADE,
        CONSTRAINT FK_UserRoles_Roles FOREIGN KEY (roleId) REFERENCES Roles(id) ON DELETE CASCADE,
        CONSTRAINT FK_UserRoles_AssignedBy FOREIGN KEY (assignedBy) REFERENCES Users(id),
        CONSTRAINT UQ_UserRoles_UserRole UNIQUE (userId, roleId)
    );

    -- Create indexes
    CREATE INDEX IX_UserRoles_UserId ON UserRoles(userId);
    CREATE INDEX IX_UserRoles_RoleId ON UserRoles(roleId);
    CREATE INDEX IX_UserRoles_IsActive ON UserRoles(isActive);

    PRINT 'UserRoles table created successfully';
END
ELSE
BEGIN
    PRINT 'UserRoles table already exists';
END
GO

-- Create RolePermissions junction table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RolePermissions' AND xtype='U')
BEGIN
    CREATE TABLE RolePermissions (
        id INT PRIMARY KEY IDENTITY(1,1),
        roleId INT NOT NULL,
        permissionId INT NOT NULL,
        grantedAt DATETIME DEFAULT GETDATE(),
        grantedBy INT NULL, -- User who granted this permission

        CONSTRAINT FK_RolePermissions_Roles FOREIGN KEY (roleId) REFERENCES Roles(id) ON DELETE CASCADE,
        CONSTRAINT FK_RolePermissions_Permissions FOREIGN KEY (permissionId) REFERENCES Permissions(id) ON DELETE CASCADE,
        CONSTRAINT FK_RolePermissions_GrantedBy FOREIGN KEY (grantedBy) REFERENCES Users(id),
        CONSTRAINT UQ_RolePermissions_RolePermission UNIQUE (roleId, permissionId)
    );

    -- Create indexes
    CREATE INDEX IX_RolePermissions_RoleId ON RolePermissions(roleId);
    CREATE INDEX IX_RolePermissions_PermissionId ON RolePermissions(permissionId);

    PRINT 'RolePermissions table created successfully';
END
ELSE
BEGIN
    PRINT 'RolePermissions table already exists';
END
GO

-- Assign default permissions to roles
-- Admin gets all permissions
DECLARE @adminRoleId INT = (SELECT id FROM Roles WHERE name = 'Admin');
INSERT INTO RolePermissions (roleId, permissionId)
SELECT @adminRoleId, id FROM Permissions;

-- Employee gets limited permissions
DECLARE @employeeRoleId INT = (SELECT id FROM Roles WHERE name = 'Employee');
INSERT INTO RolePermissions (roleId, permissionId)
SELECT @employeeRoleId, id FROM Permissions
WHERE name IN (
    'products.read', 'products.update',
    'inventory.create', 'inventory.read', 'inventory.update',
    'orders.read', 'orders.update',
    'analytics.read'
);

-- Customer gets minimal permissions
DECLARE @customerRoleId INT = (SELECT id FROM Roles WHERE name = 'Customer');
INSERT INTO RolePermissions (roleId, permissionId)
SELECT @customerRoleId, id FROM Permissions
WHERE name IN (
    'products.read',
    'orders.create', 'orders.read'
);

PRINT 'Default role permissions assigned successfully';
GO
