USE OfficeEcommerce;
GO

-- Seed ProductAttributes data
IF NOT EXISTS (SELECT * FROM ProductAttributes)
BEGIN
    -- Color attributes
    INSERT INTO ProductAttributes (name, type, value, displayName, hexColor, priceModifier, sortOrder) VALUES
    ('color_natural_wood', 'color', 'natural_wood', 'Natural Wood', '#D2B48C', 0, 1),
    ('color_dark_walnut', 'color', 'dark_walnut', 'Dark Walnut', '#654321', 50, 2),
    ('color_black', 'color', 'black', 'Black', '#000000', 25, 3),
    ('color_white', 'color', 'white', 'White', '#FFFFFF', 25, 4),
    ('color_cherry', 'color', 'cherry', 'Cherry Wood', '#8B4513', 75, 5),
    ('color_mahogany', 'color', 'mahogany', 'Mahogany', '#C04000', 100, 6),
    
    -- Material attributes
    ('material_solid_wood', 'material', 'solid_wood', 'Solid Wood', NULL, 200, 1),
    ('material_engineered_wood', 'material', 'engineered_wood', 'Engineered Wood', NULL, 0, 2),
    ('material_metal', 'material', 'metal', 'Metal Frame', NULL, 150, 3),
    ('material_glass', 'material', 'glass', 'Tempered Glass', NULL, 100, 4),
    ('material_laminate', 'material', 'laminate', 'Laminate', NULL, -50, 5),
    
    -- Finish attributes
    ('finish_matte', 'finish', 'matte', 'Matte Finish', NULL, 0, 1),
    ('finish_glossy', 'finish', 'glossy', 'Glossy Finish', NULL, 30, 2),
    ('finish_satin', 'finish', 'satin', 'Satin Finish', NULL, 20, 3),
    ('finish_textured', 'finish', 'textured', 'Textured Finish', NULL, 40, 4),
    
    -- Accessory attributes
    ('accessory_keyboard_tray', 'accessory', 'keyboard_tray', 'Keyboard Tray', NULL, 75, 1),
    ('accessory_cable_management', 'accessory', 'cable_management', 'Cable Management', NULL, 50, 2),
    ('accessory_monitor_arm', 'accessory', 'monitor_arm', 'Monitor Arm', NULL, 150, 3),
    ('accessory_desk_pad', 'accessory', 'desk_pad', 'Desk Pad', NULL, 25, 4),
    ('accessory_drawer_organizer', 'accessory', 'drawer_organizer', 'Drawer Organizer', NULL, 35, 5);

    PRINT 'ProductAttributes seeded successfully';
END
ELSE
BEGIN
    PRINT 'ProductAttributes already contain data';
END
GO

-- Seed Suppliers data
IF NOT EXISTS (SELECT * FROM Suppliers)
BEGIN
    INSERT INTO Suppliers (name, contactPerson, email, phone, address, website, paymentTerms, rating) VALUES
    ('Premium Wood Suppliers Inc.', 'John Smith', '<EMAIL>', '******-0101', '123 Industrial Ave, Woodville, NY 12345', 'www.premiumwood.com', 'Net 30', 4.5),
    ('Modern Office Solutions', 'Sarah Johnson', '<EMAIL>', '******-0102', '456 Business Blvd, Commerce City, CA 90210', 'www.modernofficeco.com', 'Net 15', 4.8),
    ('Global Furniture Manufacturing', 'Mike Chen', '<EMAIL>', '******-0103', '789 Factory St, Manufacturing District, TX 75001', 'www.globalfurniture.com', 'Net 45', 4.2),
    ('Eco-Friendly Materials Co.', 'Lisa Green', '<EMAIL>', '******-0104', '321 Green Way, Sustainable City, OR 97001', 'www.ecofriendly.com', 'Net 30', 4.7),
    ('Metal Works Industries', 'Robert Steel', '<EMAIL>', '******-0105', '654 Steel Ave, Industrial Park, PA 19001', 'www.metalworks.com', 'Net 20', 4.3);

    PRINT 'Suppliers seeded successfully';
END
ELSE
BEGIN
    PRINT 'Suppliers already contain data';
END
GO

-- Seed ProductVariants for existing products
IF NOT EXISTS (SELECT * FROM ProductVariants)
BEGIN
    DECLARE @deskProductId INT = (SELECT TOP 1 id FROM Products WHERE name LIKE '%Executive%Desk%');
    DECLARE @chairProductId INT = (SELECT TOP 1 id FROM Products WHERE name LIKE '%Executive%Chair%');
    
    IF @deskProductId IS NOT NULL
    BEGIN
        INSERT INTO ProductVariants (productId, sku, variantName, attributes, priceModifier, dimensions) VALUES
        (@deskProductId, 'EXEC-DESK-NW-STD', 'Executive Desk - Natural Wood Standard', '{"color": "natural_wood", "material": "solid_wood", "finish": "matte", "size": "standard"}', 0, '{"width": 180, "height": 75, "depth": 90}'),
        (@deskProductId, 'EXEC-DESK-DW-STD', 'Executive Desk - Dark Walnut Standard', '{"color": "dark_walnut", "material": "solid_wood", "finish": "satin", "size": "standard"}', 50, '{"width": 180, "height": 75, "depth": 90}'),
        (@deskProductId, 'EXEC-DESK-CH-LRG', 'Executive Desk - Cherry Large', '{"color": "cherry", "material": "solid_wood", "finish": "glossy", "size": "large"}', 175, '{"width": 200, "height": 75, "depth": 100}');
    END
    
    IF @chairProductId IS NOT NULL
    BEGIN
        INSERT INTO ProductVariants (productId, sku, variantName, attributes, priceModifier, dimensions) VALUES
        (@chairProductId, 'EXEC-CHAIR-BLK-STD', 'Executive Chair - Black Standard', '{"color": "black", "material": "leather", "size": "standard"}', 0, '{"width": 70, "height": 120, "depth": 70}'),
        (@chairProductId, 'EXEC-CHAIR-BRN-STD', 'Executive Chair - Brown Standard', '{"color": "brown", "material": "leather", "size": "standard"}', 25, '{"width": 70, "height": 120, "depth": 70}');
    END

    PRINT 'ProductVariants seeded successfully';
END
ELSE
BEGIN
    PRINT 'ProductVariants already contain data';
END
GO

-- Seed Inventory data for products and variants
IF NOT EXISTS (SELECT * FROM Inventory)
BEGIN
    -- Inventory for base products
    INSERT INTO Inventory (productId, supplierId, sku, quantityOnHand, reorderLevel, unitCost, location)
    SELECT 
        p.id,
        (SELECT TOP 1 id FROM Suppliers ORDER BY NEWID()), -- Random supplier
        CONCAT('INV-', p.id, '-BASE'),
        FLOOR(RAND() * 100) + 10, -- Random quantity between 10-109
        15, -- Reorder level
        p.price * 0.6, -- Cost is 60% of selling price
        CASE (p.id % 3)
            WHEN 0 THEN 'Warehouse A'
            WHEN 1 THEN 'Warehouse B'
            ELSE 'Warehouse C'
        END
    FROM Products p
    WHERE p.inStock = 1;

    -- Inventory for product variants
    INSERT INTO Inventory (variantId, supplierId, sku, quantityOnHand, reorderLevel, unitCost, location)
    SELECT 
        pv.id,
        (SELECT TOP 1 id FROM Suppliers ORDER BY NEWID()), -- Random supplier
        pv.sku,
        FLOOR(RAND() * 50) + 5, -- Random quantity between 5-54
        10, -- Reorder level
        (SELECT price FROM Products WHERE id = pv.productId) * 0.6 + pv.priceModifier * 0.6, -- Cost calculation
        CASE (pv.id % 3)
            WHEN 0 THEN 'Warehouse A'
            WHEN 1 THEN 'Warehouse B'
            ELSE 'Warehouse C'
        END
    FROM ProductVariants pv;

    PRINT 'Inventory seeded successfully';
END
ELSE
BEGIN
    PRINT 'Inventory already contains data';
END
GO
