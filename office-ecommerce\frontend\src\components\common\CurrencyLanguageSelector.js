import React, { useState, useRef, useEffect } from 'react';

const CurrencyLanguageSelector = () => {
    const [selectedCurrency, setSelectedCurrency] = useState('USD');
    const [selectedLanguage, setSelectedLanguage] = useState('English');
    const [isCurrencyOpen, setIsCurrencyOpen] = useState(false);
    const [isLanguageOpen, setIsLanguageOpen] = useState(false);
    
    const currencyRef = useRef(null);
    const languageRef = useRef(null);

    const currencies = [
        { code: 'PHP', name: 'Philippine Peso', symbol: '₱' },
        { code: 'USD', name: 'US Dollar', symbol: '$' }
    ];

    const languages = [
        { code: 'en', name: 'English' },
        { code: 'fil', name: 'Filipino' }
    ];

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (currencyRef.current && !currencyRef.current.contains(event.target)) {
                setIsCurrencyOpen(false);
            }
            if (languageRef.current && !languageRef.current.contains(event.target)) {
                setIsLanguageOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleCurrencySelect = (currency) => {
        setSelectedCurrency(currency.code);
        setIsCurrencyOpen(false);
        // Here you can add logic to update prices throughout the app
        console.log('Currency changed to:', currency.code);
    };

    const handleLanguageSelect = (language) => {
        setSelectedLanguage(language.name);
        setIsLanguageOpen(false);
        // Here you can add logic to change app language
        console.log('Language changed to:', language.name);
    };

    return (
        <div className="currency-language-selector">
            {/* Currency Selector */}
            <div className="selector-dropdown" ref={currencyRef}>
                <button 
                    className="selector-button"
                    onClick={() => {
                        setIsCurrencyOpen(!isCurrencyOpen);
                        setIsLanguageOpen(false);
                    }}
                >
                    <span>{selectedCurrency}</span>
                    <svg 
                        width="12" 
                        height="12" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        xmlns="http://www.w3.org/2000/svg"
                        className={`dropdown-arrow ${isCurrencyOpen ? 'open' : ''}`}
                    >
                        <path 
                            d="M6 9L12 15L18 9" 
                            stroke="currentColor" 
                            strokeWidth="2" 
                            strokeLinecap="round" 
                            strokeLinejoin="round"
                        />
                    </svg>
                </button>
                
                {isCurrencyOpen && (
                    <div className="selector-dropdown-menu">
                        {currencies.map((currency) => (
                            <button
                                key={currency.code}
                                className={`dropdown-item ${selectedCurrency === currency.code ? 'selected' : ''}`}
                                onClick={() => handleCurrencySelect(currency)}
                            >
                                <span className="currency-code">{currency.code}</span>
                                <span className="currency-name">- {currency.name}</span>
                            </button>
                        ))}
                    </div>
                )}
            </div>

            {/* Language Selector */}
            <div className="selector-dropdown" ref={languageRef}>
                <button 
                    className="selector-button"
                    onClick={() => {
                        setIsLanguageOpen(!isLanguageOpen);
                        setIsCurrencyOpen(false);
                    }}
                >
                    <span>{selectedLanguage}</span>
                    <svg 
                        width="12" 
                        height="12" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        xmlns="http://www.w3.org/2000/svg"
                        className={`dropdown-arrow ${isLanguageOpen ? 'open' : ''}`}
                    >
                        <path 
                            d="M6 9L12 15L18 9" 
                            stroke="currentColor" 
                            strokeWidth="2" 
                            strokeLinecap="round" 
                            strokeLinejoin="round"
                        />
                    </svg>
                </button>
                
                {isLanguageOpen && (
                    <div className="selector-dropdown-menu">
                        {languages.map((language) => (
                            <button
                                key={language.code}
                                className={`dropdown-item ${selectedLanguage === language.name ? 'selected' : ''}`}
                                onClick={() => handleLanguageSelect(language)}
                            >
                                {language.name}
                            </button>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default CurrencyLanguageSelector;
