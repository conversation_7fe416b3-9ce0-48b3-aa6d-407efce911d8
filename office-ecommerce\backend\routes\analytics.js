const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { requirePermission, requireStaff } = require('../middleware/rbac');
const {
    getDashboardMetrics,
    getSalesAnalytics,
    getProductPerformance,
    getCustomerAnalytics
} = require('../controllers/analyticsController');

// All analytics routes require authentication and staff role
router.use(authenticateToken);
router.use(requireStaff);

// GET /api/analytics/dashboard - Get dashboard metrics
router.get('/dashboard', requirePermission('analytics.read'), getDashboardMetrics);

// GET /api/analytics/sales - Get sales analytics
router.get('/sales', requirePermission('analytics.read'), getSalesAnalytics);

// GET /api/analytics/products - Get product performance analytics
router.get('/products', requirePermission('analytics.read'), getProductPerformance);

// GET /api/analytics/customers - Get customer analytics
router.get('/customers', requirePermission('analytics.read'), getCustomerAnalytics);

module.exports = router;
