import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const Gallery = () => {
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [selectedImage, setSelectedImage] = useState(null);

    const categories = [
        { id: 'all', name: 'All' },
        { id: 'desks', name: '<PERSON><PERSON>' },
        { id: 'chairs', name: 'Chairs' },
        { id: 'storage', name: 'Storage' },
        { id: 'conference', name: 'Conference' }
    ];

    const galleryImages = [
        {
            id: 1,
            src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop',
            category: 'desks',
            title: 'Executive Mahogany Desk',
            description: 'Premium executive workspace'
        },
        {
            id: 2,
            src: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=600&h=400&fit=crop',
            category: 'desks',
            title: 'Modern Glass Desk',
            description: 'Contemporary design meets functionality'
        },
        {
            id: 3,
            src: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop',
            category: 'chairs',
            title: 'Ergonomic Executive Chair',
            description: 'Comfort and style combined'
        },
        {
            id: 4,
            src: 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=600&h=400&fit=crop',
            category: 'storage',
            title: 'Executive Filing Cabinet',
            description: 'Organized storage solutions'
        },
        {
            id: 5,
            src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop',
            category: 'conference',
            title: 'Conference Room Setup',
            description: 'Professional meeting spaces'
        },
        {
            id: 6,
            src: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=600&h=400&fit=crop',
            category: 'desks',
            title: 'Standing Desk Setup',
            description: 'Health-conscious workspace'
        },
        {
            id: 7,
            src: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=600&h=400&fit=crop',
            category: 'chairs',
            title: 'Modern Office Chairs',
            description: 'Contemporary seating solutions'
        },
        {
            id: 8,
            src: 'https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=600&h=400&fit=crop',
            category: 'storage',
            title: 'Modern Bookshelf',
            description: 'Stylish storage and display'
        }
    ];

    const filteredImages = selectedCategory === 'all' 
        ? galleryImages 
        : galleryImages.filter(img => img.category === selectedCategory);

    const openModal = (image) => {
        setSelectedImage(image);
    };

    const closeModal = () => {
        setSelectedImage(null);
    };

    return (
        <div className="gallery-page">
            <div className="container">
                <div className="breadcrumb">
                    <Link to="/">Home</Link> / 
                    <span>Gallery</span>
                </div>

                <section className="gallery-header">
                    <h1>Our Gallery</h1>
                    <p>Explore our collection of premium office furniture in real workspace settings</p>
                </section>

                <section className="gallery-filters">
                    <div className="filter-buttons">
                        {categories.map(category => (
                            <button
                                key={category.id}
                                className={`filter-btn ${selectedCategory === category.id ? 'active' : ''}`}
                                onClick={() => setSelectedCategory(category.id)}
                            >
                                {category.name}
                            </button>
                        ))}
                    </div>
                </section>

                <section className="gallery-grid">
                    {filteredImages.map(image => (
                        <div 
                            key={image.id} 
                            className="gallery-item"
                            onClick={() => openModal(image)}
                        >
                            <img src={image.src} alt={image.title} />
                            <div className="gallery-overlay">
                                <h3>{image.title}</h3>
                                <p>{image.description}</p>
                                <button className="view-btn">View Details</button>
                            </div>
                        </div>
                    ))}
                </section>

                {filteredImages.length === 0 && (
                    <div className="no-results">
                        <p>No images found for the selected category.</p>
                    </div>
                )}

                <section className="gallery-cta">
                    <div className="cta-content">
                        <h2>Inspired by What You See?</h2>
                        <p>Browse our complete collection and find the perfect pieces for your workspace</p>
                        <Link to="/products" className="btn btn-primary">
                            Shop Now
                        </Link>
                    </div>
                </section>
            </div>

            {/* Modal */}
            {selectedImage && (
                <div className="modal-overlay" onClick={closeModal}>
                    <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                        <button className="modal-close" onClick={closeModal}>×</button>
                        <img src={selectedImage.src} alt={selectedImage.title} />
                        <div className="modal-info">
                            <h3>{selectedImage.title}</h3>
                            <p>{selectedImage.description}</p>
                            <Link 
                                to="/products" 
                                className="btn btn-primary"
                                onClick={closeModal}
                            >
                                View Products
                            </Link>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Gallery;
