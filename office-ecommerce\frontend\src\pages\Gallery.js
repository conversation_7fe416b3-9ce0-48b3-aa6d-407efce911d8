import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import '../styles/gallery.css';

const Gallery = () => {
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [selectedImage, setSelectedImage] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'masonry'
    const galleryRef = useRef(null);

    const categories = [
        { id: 'all', name: 'All', icon: '🏢', count: 12 },
        { id: 'desks', name: 'Desks', icon: '🖥️', count: 4 },
        { id: 'chairs', name: 'Chairs', icon: '🪑', count: 3 },
        { id: 'storage', name: 'Storage', icon: '📚', count: 2 },
        { id: 'conference', name: 'Conference', icon: '🤝', count: 2 },
        { id: 'workspace', name: 'Workspace', icon: '💼', count: 1 }
    ];

    const galleryImages = [
        {
            id: 1,
            src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',
            category: 'desks',
            title: 'Executive Mahogany Desk',
            description: 'Premium executive workspace with rich mahogany finish',
            tags: ['executive', 'mahogany', 'premium'],
            featured: true,
            size: 'large'
        },
        {
            id: 2,
            src: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=600&h=800&fit=crop',
            category: 'desks',
            title: 'Modern Glass Desk',
            description: 'Contemporary design meets functionality',
            tags: ['modern', 'glass', 'contemporary'],
            featured: false,
            size: 'medium'
        },
        {
            id: 3,
            src: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop',
            category: 'chairs',
            title: 'Ergonomic Executive Chair',
            description: 'Comfort and style combined for long work sessions',
            tags: ['ergonomic', 'executive', 'comfort'],
            featured: true,
            size: 'medium'
        },
        {
            id: 4,
            src: 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=600&h=600&fit=crop',
            category: 'storage',
            title: 'Executive Filing Cabinet',
            description: 'Organized storage solutions for modern offices',
            tags: ['filing', 'storage', 'organization'],
            featured: false,
            size: 'small'
        },
        {
            id: 5,
            src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=500&fit=crop',
            category: 'conference',
            title: 'Conference Room Setup',
            description: 'Professional meeting spaces that inspire collaboration',
            tags: ['conference', 'meeting', 'collaboration'],
            featured: true,
            size: 'large'
        },
        {
            id: 6,
            src: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=600&h=700&fit=crop',
            category: 'desks',
            title: 'Standing Desk Setup',
            description: 'Health-conscious workspace for active professionals',
            tags: ['standing', 'health', 'adjustable'],
            featured: false,
            size: 'medium'
        },
        {
            id: 7,
            src: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=600&h=400&fit=crop',
            category: 'chairs',
            title: 'Modern Office Chairs',
            description: 'Contemporary seating solutions for every workspace',
            tags: ['modern', 'seating', 'contemporary'],
            featured: false,
            size: 'medium'
        },
        {
            id: 8,
            src: 'https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=600&h=800&fit=crop',
            category: 'storage',
            title: 'Modern Bookshelf',
            description: 'Stylish storage and display for office libraries',
            tags: ['bookshelf', 'display', 'modern'],
            featured: false,
            size: 'medium'
        },
        {
            id: 9,
            src: 'https://images.unsplash.com/photo-1497366412874-3415097a27e7?w=800&h=600&fit=crop',
            category: 'workspace',
            title: 'Complete Office Setup',
            description: 'Fully furnished modern office workspace',
            tags: ['complete', 'modern', 'workspace'],
            featured: true,
            size: 'large'
        },
        {
            id: 10,
            src: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=600&fit=crop',
            category: 'chairs',
            title: 'Mesh Task Chair',
            description: 'Breathable mesh design for all-day comfort',
            tags: ['mesh', 'task', 'breathable'],
            featured: false,
            size: 'small'
        },
        {
            id: 11,
            src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=500&fit=crop',
            category: 'desks',
            title: 'L-Shaped Executive Desk',
            description: 'Spacious corner desk for maximum productivity',
            tags: ['l-shaped', 'corner', 'spacious'],
            featured: false,
            size: 'medium'
        },
        {
            id: 12,
            src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop',
            category: 'conference',
            title: 'Boardroom Table',
            description: 'Impressive boardroom table for executive meetings',
            tags: ['boardroom', 'executive', 'meetings'],
            featured: false,
            size: 'large'
        }
    ];

    // Enhanced filtering with search
    const filteredImages = galleryImages.filter(img => {
        const matchesCategory = selectedCategory === 'all' || img.category === selectedCategory;
        const matchesSearch = searchTerm === '' ||
            img.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            img.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
            img.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
        return matchesCategory && matchesSearch;
    });

    // Loading simulation
    useEffect(() => {
        setIsLoading(true);
        const timer = setTimeout(() => {
            setIsLoading(false);
        }, 800);
        return () => clearTimeout(timer);
    }, [selectedCategory, searchTerm]);

    // Intersection Observer for animations
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            },
            { threshold: 0.1 }
        );

        const galleryItems = galleryRef.current?.querySelectorAll('.gallery-item');
        galleryItems?.forEach((item) => observer.observe(item));

        return () => observer.disconnect();
    }, [filteredImages]);

    const openModal = (image) => {
        setSelectedImage(image);
        document.body.style.overflow = 'hidden';
    };

    const closeModal = () => {
        setSelectedImage(null);
        document.body.style.overflow = 'unset';
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Escape') closeModal();
    };

    useEffect(() => {
        document.addEventListener('keydown', handleKeyPress);
        return () => document.removeEventListener('keydown', handleKeyPress);
    }, []);

    return (
        <div className="gallery-page">
            {/* Hero Section */}
            <section className="gallery-hero">
                <div className="hero-background">
                    <div className="hero-overlay"></div>
                </div>
                <div className="container">
                    <div className="hero-content">
                        <div className="breadcrumb">
                            <Link to="/">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" strokeWidth="2"/>
                                    <path d="M9 22V12H15V22" stroke="currentColor" strokeWidth="2"/>
                                </svg>
                                Home
                            </Link>
                            <span>/</span>
                            <span>Gallery</span>
                        </div>
                        <h1 className="hero-title">
                            <span className="title-main">Design Gallery</span>
                            <span className="title-sub">Where Inspiration Meets Innovation</span>
                        </h1>
                        <p className="hero-description">
                            Discover our curated collection of premium office furniture showcased in real workspace environments.
                            From executive suites to collaborative spaces, find the perfect pieces to transform your office.
                        </p>
                        <div className="hero-stats">
                            <div className="stat">
                                <span className="stat-number">{galleryImages.length}</span>
                                <span className="stat-label">Showcase Images</span>
                            </div>
                            <div className="stat">
                                <span className="stat-number">{categories.length - 1}</span>
                                <span className="stat-label">Categories</span>
                            </div>
                            <div className="stat">
                                <span className="stat-number">100+</span>
                                <span className="stat-label">Products</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <div className="container">
                {/* Search and Filters */}
                <section className="gallery-controls">
                    <div className="controls-header">
                        <div className="search-container">
                            <div className="search-input-wrapper">
                                <svg className="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2"/>
                                    <path d="M21 21L16.65 16.65" stroke="currentColor" strokeWidth="2"/>
                                </svg>
                                <input
                                    type="text"
                                    placeholder="Search gallery..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="search-input"
                                />
                                {searchTerm && (
                                    <button
                                        className="clear-search"
                                        onClick={() => setSearchTerm('')}
                                    >
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2"/>
                                            <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2"/>
                                        </svg>
                                    </button>
                                )}
                            </div>
                        </div>

                        <div className="view-controls">
                            <button
                                className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
                                onClick={() => setViewMode('grid')}
                                title="Grid View"
                            >
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <rect x="3" y="3" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                                    <rect x="14" y="3" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                                    <rect x="14" y="14" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                                    <rect x="3" y="14" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                                </svg>
                            </button>
                            <button
                                className={`view-btn ${viewMode === 'masonry' ? 'active' : ''}`}
                                onClick={() => setViewMode('masonry')}
                                title="Masonry View"
                            >
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <rect x="3" y="3" width="7" height="9" stroke="currentColor" strokeWidth="2"/>
                                    <rect x="14" y="3" width="7" height="5" stroke="currentColor" strokeWidth="2"/>
                                    <rect x="14" y="12" width="7" height="9" stroke="currentColor" strokeWidth="2"/>
                                    <rect x="3" y="16" width="7" height="5" stroke="currentColor" strokeWidth="2"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <div className="filter-categories">
                        {categories.map(category => (
                            <button
                                key={category.id}
                                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                                onClick={() => setSelectedCategory(category.id)}
                            >
                                <span className="category-icon">{category.icon}</span>
                                <span className="category-name">{category.name}</span>
                                <span className="category-count">{category.count}</span>
                            </button>
                        ))}
                    </div>

                    <div className="results-info">
                        <span className="results-count">
                            {isLoading ? 'Loading...' : `${filteredImages.length} ${filteredImages.length === 1 ? 'image' : 'images'} found`}
                        </span>
                        {searchTerm && (
                            <span className="search-term">for "{searchTerm}"</span>
                        )}
                    </div>
                </section>

                {/* Gallery Grid */}
                <section className={`gallery-grid ${viewMode}`} ref={galleryRef}>
                    {isLoading ? (
                        // Loading skeleton
                        Array.from({ length: 8 }).map((_, index) => (
                            <div key={index} className="gallery-item skeleton">
                                <div className="skeleton-image"></div>
                                <div className="skeleton-content">
                                    <div className="skeleton-title"></div>
                                    <div className="skeleton-description"></div>
                                </div>
                            </div>
                        ))
                    ) : filteredImages.length > 0 ? (
                        filteredImages.map((image, index) => (
                            <div
                                key={image.id}
                                className={`gallery-item ${image.size} ${image.featured ? 'featured' : ''}`}
                                onClick={() => openModal(image)}
                                style={{ animationDelay: `${index * 0.1}s` }}
                            >
                                <div className="image-container">
                                    <img
                                        src={image.src}
                                        alt={image.title}
                                        loading="lazy"
                                    />
                                    {image.featured && (
                                        <div className="featured-badge">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" fill="currentColor"/>
                                            </svg>
                                            Featured
                                        </div>
                                    )}
                                    <div className="image-overlay">
                                        <div className="overlay-content">
                                            <h3 className="image-title">{image.title}</h3>
                                            <p className="image-description">{image.description}</p>
                                            <div className="image-tags">
                                                {image.tags.slice(0, 3).map(tag => (
                                                    <span key={tag} className="tag">#{tag}</span>
                                                ))}
                                            </div>
                                            <div className="overlay-actions">
                                                <button className="action-btn primary">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                        <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" strokeWidth="2"/>
                                                        <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
                                                    </svg>
                                                    View Details
                                                </button>
                                                <button className="action-btn secondary">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                        <path d="M19 14C19.5523 14 20 13.5523 20 13C20 12.4477 19.5523 12 19 12C18.4477 12 18 12.4477 18 13C18 13.5523 18.4477 14 19 14Z" stroke="currentColor" strokeWidth="2"/>
                                                        <path d="M5 14C5.55228 14 6 13.5523 6 13C6 12.4477 5.55228 12 5 12C4.44772 12 4 12.4477 4 13C4 13.5523 4.44772 14 5 14Z" stroke="currentColor" strokeWidth="2"/>
                                                        <path d="M12 14C12.5523 14 13 13.5523 13 13C13 12.4477 12.5523 12 12 12C11.4477 12 11 12.4477 11 13C11 13.5523 11.4477 14 12 14Z" stroke="currentColor" strokeWidth="2"/>
                                                    </svg>
                                                    More
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="no-results">
                            <div className="no-results-icon">
                                <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
                                    <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2"/>
                                    <path d="M21 21L16.65 16.65" stroke="currentColor" strokeWidth="2"/>
                                </svg>
                            </div>
                            <h3>No images found</h3>
                            <p>Try adjusting your search terms or category filters</p>
                            <button
                                className="btn btn-outline"
                                onClick={() => {
                                    setSearchTerm('');
                                    setSelectedCategory('all');
                                }}
                            >
                                Clear Filters
                            </button>
                        </div>
                    )}
                </section>

                {/* Call to Action */}
                <section className="gallery-cta">
                    <div className="cta-background">
                        <div className="cta-pattern"></div>
                    </div>
                    <div className="cta-content">
                        <div className="cta-text">
                            <h2>Ready to Transform Your Workspace?</h2>
                            <p>
                                Get inspired by our gallery and discover the perfect furniture pieces
                                to create your ideal office environment. From executive suites to
                                collaborative spaces, we have everything you need.
                            </p>
                            <div className="cta-features">
                                <div className="feature">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                    <span>Premium Quality</span>
                                </div>
                                <div className="feature">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                    <span>Custom Solutions</span>
                                </div>
                                <div className="feature">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                    <span>Expert Support</span>
                                </div>
                            </div>
                        </div>
                        <div className="cta-actions">
                            <Link to="/products" className="btn btn-primary btn-large">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z" stroke="currentColor" strokeWidth="2"/>
                                    <path d="M3 6H21" stroke="currentColor" strokeWidth="2"/>
                                    <path d="M16 10C16 11.0609 15.5786 12.0783 14.8284 12.8284C14.0783 13.5786 13.0609 14 12 14C10.9391 14 9.92172 13.5786 9.17157 12.8284C8.42143 12.0783 8 11.0609 8 10" stroke="currentColor" strokeWidth="2"/>
                                </svg>
                                Shop Collection
                            </Link>
                            <Link to="/contact" className="btn btn-outline btn-large">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                    <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" stroke="currentColor" strokeWidth="2"/>
                                </svg>
                                Get Consultation
                            </Link>
                        </div>
                    </div>
                </section>
            </div>

            {/* Enhanced Modal */}
            {selectedImage && (
                <div className="modal-overlay" onClick={closeModal}>
                    <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                        <button className="modal-close" onClick={closeModal}>
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2"/>
                                <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2"/>
                            </svg>
                        </button>

                        <div className="modal-image">
                            <img src={selectedImage.src} alt={selectedImage.title} />
                            {selectedImage.featured && (
                                <div className="modal-featured-badge">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" fill="currentColor"/>
                                    </svg>
                                    Featured
                                </div>
                            )}
                        </div>

                        <div className="modal-info">
                            <div className="modal-header">
                                <h3>{selectedImage.title}</h3>
                                <span className="modal-category">{selectedImage.category}</span>
                            </div>
                            <p className="modal-description">{selectedImage.description}</p>

                            <div className="modal-tags">
                                {selectedImage.tags.map(tag => (
                                    <span key={tag} className="modal-tag">#{tag}</span>
                                ))}
                            </div>

                            <div className="modal-actions">
                                <Link
                                    to="/products"
                                    className="btn btn-primary"
                                    onClick={closeModal}
                                >
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z" stroke="currentColor" strokeWidth="2"/>
                                        <path d="M3 6H21" stroke="currentColor" strokeWidth="2"/>
                                    </svg>
                                    View Products
                                </Link>
                                <button className="btn btn-outline" onClick={closeModal}>
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Gallery;
