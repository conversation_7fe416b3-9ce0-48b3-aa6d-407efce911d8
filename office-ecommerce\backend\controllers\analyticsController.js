const { getPool, sql } = require('../config/database');

/**
 * Get dashboard metrics for admin/employee dashboard
 */
const getDashboardMetrics = async (req, res) => {
    try {
        const pool = getPool();
        
        const result = await pool.request().query('SELECT * FROM DashboardMetrics');
        
        res.json(result.recordset[0]);
    } catch (error) {
        console.error('Get dashboard metrics error:', error);
        res.status(500).json({
            message: 'Failed to retrieve dashboard metrics',
            code: 'DASHBOARD_METRICS_ERROR'
        });
    }
};

/**
 * Get sales analytics with date range filtering
 */
const getSalesAnalytics = async (req, res) => {
    try {
        const {
            startDate,
            endDate,
            groupBy = 'day', // day, week, month, year
            categoryId,
            productId
        } = req.query;

        const pool = getPool();
        let whereClause = 'WHERE o.status NOT IN (\'cancelled\', \'returned\')';
        const params = [];

        if (startDate) {
            whereClause += ' AND o.createdAt >= @startDate';
            params.push({ name: 'startDate', type: sql.DateTime, value: new Date(startDate) });
        }

        if (endDate) {
            whereClause += ' AND o.createdAt <= @endDate';
            params.push({ name: 'endDate', type: sql.DateTime, value: new Date(endDate) });
        }

        if (categoryId) {
            whereClause += ' AND p.categoryId = @categoryId';
            params.push({ name: 'categoryId', type: sql.Int, value: parseInt(categoryId) });
        }

        if (productId) {
            whereClause += ' AND oi.productId = @productId';
            params.push({ name: 'productId', type: sql.Int, value: parseInt(productId) });
        }

        // Determine date grouping format
        let dateFormat;
        switch (groupBy) {
            case 'week':
                dateFormat = 'DATEPART(year, o.createdAt), DATEPART(week, o.createdAt)';
                break;
            case 'month':
                dateFormat = 'YEAR(o.createdAt), MONTH(o.createdAt)';
                break;
            case 'year':
                dateFormat = 'YEAR(o.createdAt)';
                break;
            default: // day
                dateFormat = 'CAST(o.createdAt AS DATE)';
        }

        const query = `
            SELECT 
                ${dateFormat} as period,
                COUNT(DISTINCT o.id) as totalOrders,
                SUM(oi.quantity) as totalQuantitySold,
                SUM(oi.totalPrice) as totalRevenue,
                AVG(o.totalAmount) as averageOrderValue,
                COUNT(DISTINCT o.userId) as uniqueCustomers
            FROM Orders o
            INNER JOIN OrderItems oi ON o.id = oi.orderId
            LEFT JOIN Products p ON oi.productId = p.id
            ${whereClause}
            GROUP BY ${dateFormat}
            ORDER BY ${dateFormat}
        `;

        const request = pool.request();
        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const result = await request.query(query);

        res.json({
            analytics: result.recordset,
            groupBy,
            filters: { startDate, endDate, categoryId, productId }
        });
    } catch (error) {
        console.error('Get sales analytics error:', error);
        res.status(500).json({
            message: 'Failed to retrieve sales analytics',
            code: 'SALES_ANALYTICS_ERROR'
        });
    }
};

/**
 * Get product performance analytics
 */
const getProductPerformance = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            sortBy = 'totalRevenue',
            sortOrder = 'DESC',
            categoryId,
            lowStock
        } = req.query;

        const offset = (page - 1) * limit;
        const pool = getPool();

        let whereClause = 'WHERE 1=1';
        const params = [];

        if (categoryId) {
            whereClause += ' AND categoryName = @categoryName';
            // Get category name from ID
            const categoryResult = await pool.request()
                .input('categoryId', sql.Int, categoryId)
                .query('SELECT name FROM Categories WHERE id = @categoryId');
            
            if (categoryResult.recordset.length > 0) {
                params.push({ name: 'categoryName', type: sql.NVarChar, value: categoryResult.recordset[0].name });
            }
        }

        if (lowStock === 'true') {
            whereClause += ' AND isLowStock = 1';
        }

        const allowedSortFields = ['productName', 'totalSold', 'totalRevenue', 'averagePrice', 'currentStock'];
        const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'totalRevenue';
        const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

        const query = `
            SELECT *
            FROM ProductPerformance
            ${whereClause}
            ORDER BY ${sortField} ${order}
            OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM ProductPerformance
            ${whereClause}
        `;

        const request = pool.request()
            .input('offset', sql.Int, offset)
            .input('limit', sql.Int, parseInt(limit));

        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [performanceResult, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        const total = countResult.recordset[0].total;
        const totalPages = Math.ceil(total / limit);

        res.json({
            products: performanceResult.recordset,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalItems: total,
                itemsPerPage: parseInt(limit)
            }
        });
    } catch (error) {
        console.error('Get product performance error:', error);
        res.status(500).json({
            message: 'Failed to retrieve product performance',
            code: 'PRODUCT_PERFORMANCE_ERROR'
        });
    }
};

/**
 * Get customer analytics
 */
const getCustomerAnalytics = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            segment,
            sortBy = 'totalSpent',
            sortOrder = 'DESC'
        } = req.query;

        const offset = (page - 1) * limit;
        const pool = getPool();

        let whereClause = 'WHERE 1=1';
        const params = [];

        if (segment) {
            whereClause += ' AND ca.segmentType = @segment';
            params.push({ name: 'segment', type: sql.NVarChar, value: segment });
        }

        const allowedSortFields = ['totalSpent', 'totalOrders', 'averageOrderValue', 'loyaltyScore', 'daysSinceLastOrder'];
        const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'totalSpent';
        const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

        const query = `
            SELECT 
                u.id as userId,
                u.firstName + ' ' + u.lastName as customerName,
                u.email,
                ca.totalOrders,
                ca.totalSpent,
                ca.averageOrderValue,
                ca.firstOrderDate,
                ca.lastOrderDate,
                ca.daysSinceLastOrder,
                ca.loyaltyScore,
                ca.segmentType,
                ca.preferredCategory
            FROM CustomerAnalytics ca
            INNER JOIN Users u ON ca.userId = u.id
            ${whereClause}
            ORDER BY ca.${sortField} ${order}
            OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM CustomerAnalytics ca
            INNER JOIN Users u ON ca.userId = u.id
            ${whereClause}
        `;

        const request = pool.request()
            .input('offset', sql.Int, offset)
            .input('limit', sql.Int, parseInt(limit));

        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [customersResult, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        // Get segment distribution
        const segmentResult = await pool.request().query(`
            SELECT 
                segmentType,
                COUNT(*) as count,
                AVG(totalSpent) as avgSpent
            FROM CustomerAnalytics
            WHERE segmentType IS NOT NULL
            GROUP BY segmentType
        `);

        const total = countResult.recordset[0].total;
        const totalPages = Math.ceil(total / limit);

        res.json({
            customers: customersResult.recordset,
            segments: segmentResult.recordset,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalItems: total,
                itemsPerPage: parseInt(limit)
            }
        });
    } catch (error) {
        console.error('Get customer analytics error:', error);
        res.status(500).json({
            message: 'Failed to retrieve customer analytics',
            code: 'CUSTOMER_ANALYTICS_ERROR'
        });
    }
};

module.exports = {
    getDashboardMetrics,
    getSalesAnalytics,
    getProductPerformance,
    getCustomerAnalytics
};
