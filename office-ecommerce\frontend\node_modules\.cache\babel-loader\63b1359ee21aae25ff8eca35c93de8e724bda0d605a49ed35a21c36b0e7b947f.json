{"ast": null, "code": "var _jsxFileName = \"C:\\\\DesignXcel\\\\office-ecommerce\\\\frontend\\\\src\\\\pages\\\\Gallery.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport '../styles/gallery.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Gallery = () => {\n  _s();\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'masonry'\n  const galleryRef = useRef(null);\n  const categories = [{\n    id: 'all',\n    name: 'All'\n  }, {\n    id: 'desks',\n    name: '<PERSON><PERSON>'\n  }, {\n    id: 'chairs',\n    name: 'Chairs'\n  }, {\n    id: 'storage',\n    name: 'Storage'\n  }, {\n    id: 'conference',\n    name: 'Conference'\n  }];\n  const galleryImages = [{\n    id: 1,\n    src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop',\n    category: 'desks',\n    title: 'Executive Mahogany Desk'\n  }, {\n    id: 2,\n    src: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=600&h=400&fit=crop',\n    category: 'desks',\n    title: 'Modern Glass Desk'\n  }, {\n    id: 3,\n    src: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop',\n    category: 'chairs',\n    title: 'Ergonomic Executive Chair'\n  }, {\n    id: 4,\n    src: 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=600&h=400&fit=crop',\n    category: 'storage',\n    title: 'Executive Filing Cabinet'\n  }, {\n    id: 5,\n    src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop',\n    category: 'conference',\n    title: 'Conference Room Setup'\n  }, {\n    id: 6,\n    src: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=600&h=400&fit=crop',\n    category: 'desks',\n    title: 'Standing Desk Setup'\n  }, {\n    id: 7,\n    src: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=600&h=400&fit=crop',\n    category: 'chairs',\n    title: 'Modern Office Chairs'\n  }, {\n    id: 8,\n    src: 'https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=600&h=400&fit=crop',\n    category: 'storage',\n    title: 'Modern Bookshelf'\n  }];\n\n  // Simple filtering\n  const filteredImages = galleryImages.filter(img => {\n    const matchesCategory = selectedCategory === 'all' || img.category === selectedCategory;\n    const matchesSearch = searchTerm === '' || img.title.toLowerCase().includes(searchTerm.toLowerCase());\n    return matchesCategory && matchesSearch;\n  });\n\n  // Loading simulation\n  useEffect(() => {\n    setIsLoading(true);\n    const timer = setTimeout(() => {\n      setIsLoading(false);\n    }, 800);\n    return () => clearTimeout(timer);\n  }, [selectedCategory, searchTerm]);\n\n  // Intersection Observer for animations\n  useEffect(() => {\n    var _galleryRef$current;\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('animate-in');\n        }\n      });\n    }, {\n      threshold: 0.1\n    });\n    const galleryItems = (_galleryRef$current = galleryRef.current) === null || _galleryRef$current === void 0 ? void 0 : _galleryRef$current.querySelectorAll('.gallery-item');\n    galleryItems === null || galleryItems === void 0 ? void 0 : galleryItems.forEach(item => observer.observe(item));\n    return () => observer.disconnect();\n  }, [filteredImages]);\n  const openModal = image => {\n    setSelectedImage(image);\n    document.body.style.overflow = 'hidden';\n  };\n  const closeModal = () => {\n    setSelectedImage(null);\n    document.body.style.overflow = 'unset';\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Escape') closeModal();\n  };\n  useEffect(() => {\n    document.addEventListener('keydown', handleKeyPress);\n    return () => document.removeEventListener('keydown', handleKeyPress);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"gallery-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gallery-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"breadcrumb\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 25\n          }, this), \" / \", /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Gallery\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 52\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Gallery\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Explore our office furniture collection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"gallery-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"search-input-wrapper\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"search-icon\",\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"11\",\n                  cy: \"11\",\n                  r: \"8\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M21 21L16.65 16.65\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search gallery...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"search-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 33\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"clear-search\",\n                onClick: () => setSearchTerm(''),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"18\",\n                    y1: \"6\",\n                    x2: \"6\",\n                    y2: \"18\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n                    x1: \"6\",\n                    y1: \"6\",\n                    x2: \"18\",\n                    y2: \"18\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"view-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `view-btn ${viewMode === 'grid' ? 'active' : ''}`,\n              onClick: () => setViewMode('grid'),\n              title: \"Grid View\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"14\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"14\",\n                  width: \"7\",\n                  height: \"7\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `view-btn ${viewMode === 'masonry' ? 'active' : ''}`,\n              onClick: () => setViewMode('masonry'),\n              title: \"Masonry View\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"9\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"3\",\n                  width: \"7\",\n                  height: \"5\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"14\",\n                  y: \"12\",\n                  width: \"7\",\n                  height: \"9\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                  x: \"3\",\n                  y: \"16\",\n                  width: \"7\",\n                  height: \"5\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-categories\",\n          children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `category-btn ${selectedCategory === category.id ? 'active' : ''}`,\n            onClick: () => setSelectedCategory(category.id),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"category-icon\",\n              children: category.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"category-name\",\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"category-count\",\n              children: category.count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 33\n            }, this)]\n          }, category.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"results-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"results-count\",\n            children: isLoading ? 'Loading...' : `${filteredImages.length} ${filteredImages.length === 1 ? 'image' : 'images'} found`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 25\n          }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"search-term\",\n            children: [\"for \\\"\", searchTerm, \"\\\"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: `gallery-grid ${viewMode}`,\n        ref: galleryRef,\n        children: isLoading ?\n        // Loading skeleton\n        Array.from({\n          length: 8\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"gallery-item skeleton\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skeleton-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skeleton-title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skeleton-description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 33\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 29\n        }, this)) : filteredImages.length > 0 ? filteredImages.map((image, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `gallery-item ${image.size} ${image.featured ? 'featured' : ''}`,\n          onClick: () => openModal(image),\n          style: {\n            animationDelay: `${index * 0.1}s`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: image.src,\n              alt: image.title,\n              loading: \"lazy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 37\n            }, this), image.featured && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"featured-badge\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: /*#__PURE__*/_jsxDEV(\"polygon\", {\n                  points: \"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\",\n                  fill: \"currentColor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 45\n              }, this), \"Featured\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-overlay\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overlay-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"image-title\",\n                  children: image.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"image-description\",\n                  children: image.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"image-tags\",\n                  children: image.tags.slice(0, 3).map(tag => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"tag\",\n                    children: [\"#\", tag]\n                  }, tag, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 53\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overlay-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn primary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 267,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"3\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 268,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 53\n                    }, this), \"View Details\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"action-btn secondary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M19 14C19.5523 14 20 13.5523 20 13C20 12.4477 19.5523 12 19 12C18.4477 12 18 12.4477 18 13C18 13.5523 18.4477 14 19 14Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 274,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M5 14C5.55228 14 6 13.5523 6 13C6 12.4477 5.55228 12 5 12C4.44772 12 4 12.4477 4 13C4 13.5523 4.44772 14 5 14Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 275,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M12 14C12.5523 14 13 13.5523 13 13C13 12.4477 12.5523 12 12 12C11.4477 12 11 12.4477 11 13C11 13.5523 11.4477 14 12 14Z\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 53\n                    }, this), \"More\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 33\n          }, this)\n        }, image.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 29\n        }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-results\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-results-icon\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"64\",\n              height: \"64\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                cx: \"11\",\n                cy: \"11\",\n                r: \"8\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M21 21L16.65 16.65\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No images found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Try adjusting your search terms or category filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-outline\",\n            onClick: () => {\n              setSearchTerm('');\n              setSelectedCategory('all');\n            },\n            children: \"Clear Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"gallery-cta\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-background\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cta-pattern\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cta-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Ready to Transform Your Workspace?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Get inspired by our gallery and discover the perfect furniture pieces to create your ideal office environment. From executive suites to collaborative spaces, we have everything you need.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cta-features\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 6L9 17L4 12\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Premium Quality\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 6L9 17L4 12\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Custom Solutions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"24\",\n                  height: \"24\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M20 6L9 17L4 12\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Expert Support\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cta-actions\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              className: \"btn btn-primary btn-large\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M3 6H21\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M16 10C16 11.0609 15.5786 12.0783 14.8284 12.8284C14.0783 13.5786 13.0609 14 12 14C10.9391 14 9.92172 13.5786 9.17157 12.8284C8.42143 12.0783 8 11.0609 8 10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 33\n              }, this), \"Shop Collection\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              className: \"btn btn-outline btn-large\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 33\n              }, this), \"Get Consultation\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 13\n    }, this), selectedImage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: closeModal,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: closeModal,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [/*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"18\",\n              y1: \"6\",\n              x2: \"6\",\n              y2: \"18\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"6\",\n              y1: \"6\",\n              x2: \"18\",\n              y2: \"18\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-image\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: selectedImage.src,\n            alt: selectedImage.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 29\n          }, this), selectedImage.featured && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-featured-badge\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"16\",\n              height: \"16\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"polygon\", {\n                points: \"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\",\n                fill: \"currentColor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 37\n            }, this), \"Featured\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: selectedImage.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"modal-category\",\n              children: selectedImage.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"modal-description\",\n            children: selectedImage.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-tags\",\n            children: selectedImage.tags.map(tag => /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"modal-tag\",\n              children: [\"#\", tag]\n            }, tag, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-actions\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/products\",\n              className: \"btn btn-primary\",\n              onClick: closeModal,\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"16\",\n                height: \"16\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M3 6H21\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 37\n              }, this), \"View Products\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-outline\",\n              onClick: closeModal,\n              children: \"Close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 9\n  }, this);\n};\n_s(Gallery, \"bpzD2AgrN4HQGY5afK+JzNe/4tI=\");\n_c = Gallery;\nexport default Gallery;\nvar _c;\n$RefreshReg$(_c, \"Gallery\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "jsxDEV", "_jsxDEV", "Gallery", "_s", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedImage", "setSelectedImage", "isLoading", "setIsLoading", "searchTerm", "setSearchTerm", "viewMode", "setViewMode", "galleryRef", "categories", "id", "name", "galleryImages", "src", "category", "title", "filteredImages", "filter", "img", "matchesCategory", "matchesSearch", "toLowerCase", "includes", "timer", "setTimeout", "clearTimeout", "_galleryRef$current", "observer", "IntersectionObserver", "entries", "for<PERSON>ach", "entry", "isIntersecting", "target", "classList", "add", "threshold", "galleryItems", "current", "querySelectorAll", "item", "observe", "disconnect", "openModal", "image", "document", "body", "style", "overflow", "closeModal", "handleKeyPress", "e", "key", "addEventListener", "removeEventListener", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "fill", "cx", "cy", "r", "stroke", "strokeWidth", "d", "type", "placeholder", "value", "onChange", "onClick", "x1", "y1", "x2", "y2", "x", "y", "map", "icon", "count", "length", "ref", "Array", "from", "_", "index", "size", "featured", "animationDelay", "alt", "loading", "points", "description", "tags", "slice", "tag", "strokeLinecap", "strokeLinejoin", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["C:/DesignXcel/office-ecommerce/frontend/src/pages/Gallery.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport '../styles/gallery.css';\n\nconst Gallery = () => {\n    const [selectedCategory, setSelectedCategory] = useState('all');\n    const [selectedImage, setSelectedImage] = useState(null);\n    const [isLoading, setIsLoading] = useState(true);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'masonry'\n    const galleryRef = useRef(null);\n\n    const categories = [\n        { id: 'all', name: 'All' },\n        { id: 'desks', name: 'Des<PERSON>' },\n        { id: 'chairs', name: 'Chairs' },\n        { id: 'storage', name: 'Storage' },\n        { id: 'conference', name: 'Conference' }\n    ];\n\n    const galleryImages = [\n        {\n            id: 1,\n            src: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop',\n            category: 'desks',\n            title: 'Executive Mahogany Desk'\n        },\n        {\n            id: 2,\n            src: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=600&h=400&fit=crop',\n            category: 'desks',\n            title: 'Modern Glass Desk'\n        },\n        {\n            id: 3,\n            src: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=600&h=400&fit=crop',\n            category: 'chairs',\n            title: 'Ergonomic Executive Chair'\n        },\n        {\n            id: 4,\n            src: 'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=600&h=400&fit=crop',\n            category: 'storage',\n            title: 'Executive Filing Cabinet'\n        },\n        {\n            id: 5,\n            src: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=600&h=400&fit=crop',\n            category: 'conference',\n            title: 'Conference Room Setup'\n        },\n        {\n            id: 6,\n            src: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=600&h=400&fit=crop',\n            category: 'desks',\n            title: 'Standing Desk Setup'\n        },\n        {\n            id: 7,\n            src: 'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=600&h=400&fit=crop',\n            category: 'chairs',\n            title: 'Modern Office Chairs'\n        },\n        {\n            id: 8,\n            src: 'https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=600&h=400&fit=crop',\n            category: 'storage',\n            title: 'Modern Bookshelf'\n        }\n    ];\n\n    // Simple filtering\n    const filteredImages = galleryImages.filter(img => {\n        const matchesCategory = selectedCategory === 'all' || img.category === selectedCategory;\n        const matchesSearch = searchTerm === '' ||\n            img.title.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesCategory && matchesSearch;\n    });\n\n    // Loading simulation\n    useEffect(() => {\n        setIsLoading(true);\n        const timer = setTimeout(() => {\n            setIsLoading(false);\n        }, 800);\n        return () => clearTimeout(timer);\n    }, [selectedCategory, searchTerm]);\n\n    // Intersection Observer for animations\n    useEffect(() => {\n        const observer = new IntersectionObserver(\n            (entries) => {\n                entries.forEach((entry) => {\n                    if (entry.isIntersecting) {\n                        entry.target.classList.add('animate-in');\n                    }\n                });\n            },\n            { threshold: 0.1 }\n        );\n\n        const galleryItems = galleryRef.current?.querySelectorAll('.gallery-item');\n        galleryItems?.forEach((item) => observer.observe(item));\n\n        return () => observer.disconnect();\n    }, [filteredImages]);\n\n    const openModal = (image) => {\n        setSelectedImage(image);\n        document.body.style.overflow = 'hidden';\n    };\n\n    const closeModal = () => {\n        setSelectedImage(null);\n        document.body.style.overflow = 'unset';\n    };\n\n    const handleKeyPress = (e) => {\n        if (e.key === 'Escape') closeModal();\n    };\n\n    useEffect(() => {\n        document.addEventListener('keydown', handleKeyPress);\n        return () => document.removeEventListener('keydown', handleKeyPress);\n    }, []);\n\n    return (\n        <div className=\"gallery-page\">\n            <div className=\"container\">\n                {/* Simple Header */}\n                <div className=\"gallery-header\">\n                    <div className=\"breadcrumb\">\n                        <Link to=\"/\">Home</Link> / <span>Gallery</span>\n                    </div>\n                    <h1>Gallery</h1>\n                    <p>Explore our office furniture collection</p>\n                </div>\n                {/* Search and Filters */}\n                <section className=\"gallery-controls\">\n                    <div className=\"controls-header\">\n                        <div className=\"search-container\">\n                            <div className=\"search-input-wrapper\">\n                                <svg className=\"search-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                                <input\n                                    type=\"text\"\n                                    placeholder=\"Search gallery...\"\n                                    value={searchTerm}\n                                    onChange={(e) => setSearchTerm(e.target.value)}\n                                    className=\"search-input\"\n                                />\n                                {searchTerm && (\n                                    <button\n                                        className=\"clear-search\"\n                                        onClick={() => setSearchTerm('')}\n                                    >\n                                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                            <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                            <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                        </svg>\n                                    </button>\n                                )}\n                            </div>\n                        </div>\n\n                        <div className=\"view-controls\">\n                            <button\n                                className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}\n                                onClick={() => setViewMode('grid')}\n                                title=\"Grid View\"\n                            >\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                            </button>\n                            <button\n                                className={`view-btn ${viewMode === 'masonry' ? 'active' : ''}`}\n                                onClick={() => setViewMode('masonry')}\n                                title=\"Masonry View\"\n                            >\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <rect x=\"3\" y=\"3\" width=\"7\" height=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"3\" width=\"7\" height=\"5\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"14\" y=\"12\" width=\"7\" height=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <rect x=\"3\" y=\"16\" width=\"7\" height=\"5\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                            </button>\n                        </div>\n                    </div>\n\n                    <div className=\"filter-categories\">\n                        {categories.map(category => (\n                            <button\n                                key={category.id}\n                                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}\n                                onClick={() => setSelectedCategory(category.id)}\n                            >\n                                <span className=\"category-icon\">{category.icon}</span>\n                                <span className=\"category-name\">{category.name}</span>\n                                <span className=\"category-count\">{category.count}</span>\n                            </button>\n                        ))}\n                    </div>\n\n                    <div className=\"results-info\">\n                        <span className=\"results-count\">\n                            {isLoading ? 'Loading...' : `${filteredImages.length} ${filteredImages.length === 1 ? 'image' : 'images'} found`}\n                        </span>\n                        {searchTerm && (\n                            <span className=\"search-term\">for \"{searchTerm}\"</span>\n                        )}\n                    </div>\n                </section>\n\n                {/* Gallery Grid */}\n                <section className={`gallery-grid ${viewMode}`} ref={galleryRef}>\n                    {isLoading ? (\n                        // Loading skeleton\n                        Array.from({ length: 8 }).map((_, index) => (\n                            <div key={index} className=\"gallery-item skeleton\">\n                                <div className=\"skeleton-image\"></div>\n                                <div className=\"skeleton-content\">\n                                    <div className=\"skeleton-title\"></div>\n                                    <div className=\"skeleton-description\"></div>\n                                </div>\n                            </div>\n                        ))\n                    ) : filteredImages.length > 0 ? (\n                        filteredImages.map((image, index) => (\n                            <div\n                                key={image.id}\n                                className={`gallery-item ${image.size} ${image.featured ? 'featured' : ''}`}\n                                onClick={() => openModal(image)}\n                                style={{ animationDelay: `${index * 0.1}s` }}\n                            >\n                                <div className=\"image-container\">\n                                    <img\n                                        src={image.src}\n                                        alt={image.title}\n                                        loading=\"lazy\"\n                                    />\n                                    {image.featured && (\n                                        <div className=\"featured-badge\">\n                                            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                                <polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\" fill=\"currentColor\"/>\n                                            </svg>\n                                            Featured\n                                        </div>\n                                    )}\n                                    <div className=\"image-overlay\">\n                                        <div className=\"overlay-content\">\n                                            <h3 className=\"image-title\">{image.title}</h3>\n                                            <p className=\"image-description\">{image.description}</p>\n                                            <div className=\"image-tags\">\n                                                {image.tags.slice(0, 3).map(tag => (\n                                                    <span key={tag} className=\"tag\">#{tag}</span>\n                                                ))}\n                                            </div>\n                                            <div className=\"overlay-actions\">\n                                                <button className=\"action-btn primary\">\n                                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                                        <path d=\"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                                    </svg>\n                                                    View Details\n                                                </button>\n                                                <button className=\"action-btn secondary\">\n                                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                                        <path d=\"M19 14C19.5523 14 20 13.5523 20 13C20 12.4477 19.5523 12 19 12C18.4477 12 18 12.4477 18 13C18 13.5523 18.4477 14 19 14Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                                        <path d=\"M5 14C5.55228 14 6 13.5523 6 13C6 12.4477 5.55228 12 5 12C4.44772 12 4 12.4477 4 13C4 13.5523 4.44772 14 5 14Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                                        <path d=\"M12 14C12.5523 14 13 13.5523 13 13C13 12.4477 12.5523 12 12 12C11.4477 12 11 12.4477 11 13C11 13.5523 11.4477 14 12 14Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                                    </svg>\n                                                    More\n                                                </button>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        ))\n                    ) : (\n                        <div className=\"no-results\">\n                            <div className=\"no-results-icon\">\n                                <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                            </div>\n                            <h3>No images found</h3>\n                            <p>Try adjusting your search terms or category filters</p>\n                            <button\n                                className=\"btn btn-outline\"\n                                onClick={() => {\n                                    setSearchTerm('');\n                                    setSelectedCategory('all');\n                                }}\n                            >\n                                Clear Filters\n                            </button>\n                        </div>\n                    )}\n                </section>\n\n                {/* Call to Action */}\n                <section className=\"gallery-cta\">\n                    <div className=\"cta-background\">\n                        <div className=\"cta-pattern\"></div>\n                    </div>\n                    <div className=\"cta-content\">\n                        <div className=\"cta-text\">\n                            <h2>Ready to Transform Your Workspace?</h2>\n                            <p>\n                                Get inspired by our gallery and discover the perfect furniture pieces\n                                to create your ideal office environment. From executive suites to\n                                collaborative spaces, we have everything you need.\n                            </p>\n                            <div className=\"cta-features\">\n                                <div className=\"feature\">\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                        <path d=\"M20 6L9 17L4 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                    </svg>\n                                    <span>Premium Quality</span>\n                                </div>\n                                <div className=\"feature\">\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                        <path d=\"M20 6L9 17L4 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                    </svg>\n                                    <span>Custom Solutions</span>\n                                </div>\n                                <div className=\"feature\">\n                                    <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                        <path d=\"M20 6L9 17L4 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                                    </svg>\n                                    <span>Expert Support</span>\n                                </div>\n                            </div>\n                        </div>\n                        <div className=\"cta-actions\">\n                            <Link to=\"/products\" className=\"btn btn-primary btn-large\">\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <path d=\"M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M3 6H21\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    <path d=\"M16 10C16 11.0609 15.5786 12.0783 14.8284 12.8284C14.0783 13.5786 13.0609 14 12 14C10.9391 14 9.92172 13.5786 9.17157 12.8284C8.42143 12.0783 8 11.0609 8 10\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                                Shop Collection\n                            </Link>\n                            <Link to=\"/contact\" className=\"btn btn-outline btn-large\">\n                                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                    <path d=\"M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                </svg>\n                                Get Consultation\n                            </Link>\n                        </div>\n                    </div>\n                </section>\n            </div>\n\n            {/* Enhanced Modal */}\n            {selectedImage && (\n                <div className=\"modal-overlay\" onClick={closeModal}>\n                    <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n                        <button className=\"modal-close\" onClick={closeModal}>\n                            <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                            </svg>\n                        </button>\n\n                        <div className=\"modal-image\">\n                            <img src={selectedImage.src} alt={selectedImage.title} />\n                            {selectedImage.featured && (\n                                <div className=\"modal-featured-badge\">\n                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                        <polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\" fill=\"currentColor\"/>\n                                    </svg>\n                                    Featured\n                                </div>\n                            )}\n                        </div>\n\n                        <div className=\"modal-info\">\n                            <div className=\"modal-header\">\n                                <h3>{selectedImage.title}</h3>\n                                <span className=\"modal-category\">{selectedImage.category}</span>\n                            </div>\n                            <p className=\"modal-description\">{selectedImage.description}</p>\n\n                            <div className=\"modal-tags\">\n                                {selectedImage.tags.map(tag => (\n                                    <span key={tag} className=\"modal-tag\">#{tag}</span>\n                                ))}\n                            </div>\n\n                            <div className=\"modal-actions\">\n                                <Link\n                                    to=\"/products\"\n                                    className=\"btn btn-primary\"\n                                    onClick={closeModal}\n                                >\n                                    <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                                        <path d=\"M6 2L3 6V20C3 20.5304 3.21071 21.0391 3.58579 21.4142C3.96086 21.7893 4.46957 22 5 22H19C19.5304 22 20.0391 21.7893 20.4142 21.4142C20.7893 21.0391 21 20.5304 21 20V6L18 2H6Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                        <path d=\"M3 6H21\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                                    </svg>\n                                    View Products\n                                </Link>\n                                <button className=\"btn btn-outline\" onClick={closeModal}>\n                                    Close\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default Gallery;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAClD,MAAMkB,UAAU,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMiB,UAAU,GAAG,CACf;IAAEC,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAM,CAAC,EAC1B;IAAED,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAQ,CAAC,EAC9B;IAAED,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE;EAAS,CAAC,EAChC;IAAED,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAU,CAAC,EAClC;IAAED,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAa,CAAC,CAC3C;EAED,MAAMC,aAAa,GAAG,CAClB;IACIF,EAAE,EAAE,CAAC;IACLG,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE;EACX,CAAC,EACD;IACIL,EAAE,EAAE,CAAC;IACLG,GAAG,EAAE,gFAAgF;IACrFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE;EACX,CAAC,EACD;IACIL,EAAE,EAAE,CAAC;IACLG,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE;EACX,CAAC,EACD;IACIL,EAAE,EAAE,CAAC;IACLG,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE;EACX,CAAC,EACD;IACIL,EAAE,EAAE,CAAC;IACLG,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,YAAY;IACtBC,KAAK,EAAE;EACX,CAAC,EACD;IACIL,EAAE,EAAE,CAAC;IACLG,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE;EACX,CAAC,EACD;IACIL,EAAE,EAAE,CAAC;IACLG,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE;EACX,CAAC,EACD;IACIL,EAAE,EAAE,CAAC;IACLG,GAAG,EAAE,mFAAmF;IACxFC,QAAQ,EAAE,SAAS;IACnBC,KAAK,EAAE;EACX,CAAC,CACJ;;EAED;EACA,MAAMC,cAAc,GAAGJ,aAAa,CAACK,MAAM,CAACC,GAAG,IAAI;IAC/C,MAAMC,eAAe,GAAGrB,gBAAgB,KAAK,KAAK,IAAIoB,GAAG,CAACJ,QAAQ,KAAKhB,gBAAgB;IACvF,MAAMsB,aAAa,GAAGhB,UAAU,KAAK,EAAE,IACnCc,GAAG,CAACH,KAAK,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClB,UAAU,CAACiB,WAAW,CAAC,CAAC,CAAC;IAC9D,OAAOF,eAAe,IAAIC,aAAa;EAC3C,CAAC,CAAC;;EAEF;EACA7B,SAAS,CAAC,MAAM;IACZY,YAAY,CAAC,IAAI,CAAC;IAClB,MAAMoB,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC3BrB,YAAY,CAAC,KAAK,CAAC;IACvB,CAAC,EAAE,GAAG,CAAC;IACP,OAAO,MAAMsB,YAAY,CAACF,KAAK,CAAC;EACpC,CAAC,EAAE,CAACzB,gBAAgB,EAAEM,UAAU,CAAC,CAAC;;EAElC;EACAb,SAAS,CAAC,MAAM;IAAA,IAAAmC,mBAAA;IACZ,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CACpCC,OAAO,IAAK;MACTA,OAAO,CAACC,OAAO,CAAEC,KAAK,IAAK;QACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACtBD,KAAK,CAACE,MAAM,CAACC,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN,CAAC,EACD;MAAEC,SAAS,EAAE;IAAI,CACrB,CAAC;IAED,MAAMC,YAAY,IAAAX,mBAAA,GAAGlB,UAAU,CAAC8B,OAAO,cAAAZ,mBAAA,uBAAlBA,mBAAA,CAAoBa,gBAAgB,CAAC,eAAe,CAAC;IAC1EF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEP,OAAO,CAAEU,IAAI,IAAKb,QAAQ,CAACc,OAAO,CAACD,IAAI,CAAC,CAAC;IAEvD,OAAO,MAAMb,QAAQ,CAACe,UAAU,CAAC,CAAC;EACtC,CAAC,EAAE,CAAC1B,cAAc,CAAC,CAAC;EAEpB,MAAM2B,SAAS,GAAIC,KAAK,IAAK;IACzB3C,gBAAgB,CAAC2C,KAAK,CAAC;IACvBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;EAC3C,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrBhD,gBAAgB,CAAC,IAAI,CAAC;IACtB4C,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;EAC1C,CAAC;EAED,MAAME,cAAc,GAAIC,CAAC,IAAK;IAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAEH,UAAU,CAAC,CAAC;EACxC,CAAC;EAED1D,SAAS,CAAC,MAAM;IACZsD,QAAQ,CAACQ,gBAAgB,CAAC,SAAS,EAAEH,cAAc,CAAC;IACpD,OAAO,MAAML,QAAQ,CAACS,mBAAmB,CAAC,SAAS,EAAEJ,cAAc,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIvD,OAAA;IAAK4D,SAAS,EAAC,cAAc;IAAAC,QAAA,gBACzB7D,OAAA;MAAK4D,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAEtB7D,OAAA;QAAK4D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3B7D,OAAA;UAAK4D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB7D,OAAA,CAACF,IAAI;YAACgE,EAAE,EAAC,GAAG;YAAAD,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,OAAG,eAAAlE,OAAA;YAAA6D,QAAA,EAAM;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNlE,OAAA;UAAA6D,QAAA,EAAI;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChBlE,OAAA;UAAA6D,QAAA,EAAG;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAENlE,OAAA;QAAS4D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACjC7D,OAAA;UAAK4D,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5B7D,OAAA;YAAK4D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7B7D,OAAA;cAAK4D,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACjC7D,OAAA;gBAAK4D,SAAS,EAAC,aAAa;gBAACO,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBAC/E7D,OAAA;kBAAQuE,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,GAAG;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACrElE,OAAA;kBAAM4E,CAAC,EAAC,oBAAoB;kBAACF,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNlE,OAAA;gBACI6E,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,mBAAmB;gBAC/BC,KAAK,EAAEtE,UAAW;gBAClBuE,QAAQ,EAAGxB,CAAC,IAAK9C,aAAa,CAAC8C,CAAC,CAAClB,MAAM,CAACyC,KAAK,CAAE;gBAC/CnB,SAAS,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,EACDzD,UAAU,iBACPT,OAAA;gBACI4D,SAAS,EAAC,cAAc;gBACxBqB,OAAO,EAAEA,CAAA,KAAMvE,aAAa,CAAC,EAAE,CAAE;gBAAAmD,QAAA,eAEjC7D,OAAA;kBAAKmE,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAT,QAAA,gBACvD7D,OAAA;oBAAMkF,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,IAAI;oBAACX,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC3ElE,OAAA;oBAAMkF,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,GAAG;oBAACC,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACX,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENlE,OAAA;YAAK4D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B7D,OAAA;cACI4D,SAAS,EAAE,YAAYjD,QAAQ,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC7DsE,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,MAAM,CAAE;cACnCQ,KAAK,EAAC,WAAW;cAAAyC,QAAA,eAEjB7D,OAAA;gBAAKmE,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvD7D,OAAA;kBAAMsF,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACpB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACM,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9ElE,OAAA;kBAAMsF,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,GAAG;kBAACpB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACM,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/ElE,OAAA;kBAAMsF,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACpB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACM,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChFlE,OAAA;kBAAMsF,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,IAAI;kBAACpB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACM,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACTlE,OAAA;cACI4D,SAAS,EAAE,YAAYjD,QAAQ,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;cAChEsE,OAAO,EAAEA,CAAA,KAAMrE,WAAW,CAAC,SAAS,CAAE;cACtCQ,KAAK,EAAC,cAAc;cAAAyC,QAAA,eAEpB7D,OAAA;gBAAKmE,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvD7D,OAAA;kBAAMsF,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,GAAG;kBAACpB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACM,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9ElE,OAAA;kBAAMsF,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,GAAG;kBAACpB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACM,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/ElE,OAAA;kBAAMsF,CAAC,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACpB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACM,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChFlE,OAAA;kBAAMsF,CAAC,EAAC,GAAG;kBAACC,CAAC,EAAC,IAAI;kBAACpB,KAAK,EAAC,GAAG;kBAACC,MAAM,EAAC,GAAG;kBAACM,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENlE,OAAA;UAAK4D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC7B/C,UAAU,CAAC0E,GAAG,CAACrE,QAAQ,iBACpBnB,OAAA;YAEI4D,SAAS,EAAE,gBAAgBzD,gBAAgB,KAAKgB,QAAQ,CAACJ,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC9EkE,OAAO,EAAEA,CAAA,KAAM7E,mBAAmB,CAACe,QAAQ,CAACJ,EAAE,CAAE;YAAA8C,QAAA,gBAEhD7D,OAAA;cAAM4D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE1C,QAAQ,CAACsE;YAAI;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDlE,OAAA;cAAM4D,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAE1C,QAAQ,CAACH;YAAI;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDlE,OAAA;cAAM4D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAE1C,QAAQ,CAACuE;YAAK;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GANnD/C,QAAQ,CAACJ,EAAE;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOZ,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENlE,OAAA;UAAK4D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzB7D,OAAA;YAAM4D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC1BtD,SAAS,GAAG,YAAY,GAAG,GAAGc,cAAc,CAACsE,MAAM,IAAItE,cAAc,CAACsE,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,QAAQ;UAAQ;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G,CAAC,EACNzD,UAAU,iBACPT,OAAA;YAAM4D,SAAS,EAAC,aAAa;YAAAC,QAAA,GAAC,QAAK,EAACpD,UAAU,EAAC,IAAC;UAAA;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGVlE,OAAA;QAAS4D,SAAS,EAAE,gBAAgBjD,QAAQ,EAAG;QAACiF,GAAG,EAAE/E,UAAW;QAAAgD,QAAA,EAC3DtD,SAAS;QACN;QACAsF,KAAK,CAACC,IAAI,CAAC;UAAEH,MAAM,EAAE;QAAE,CAAC,CAAC,CAACH,GAAG,CAAC,CAACO,CAAC,EAAEC,KAAK,kBACnChG,OAAA;UAAiB4D,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAC9C7D,OAAA;YAAK4D,SAAS,EAAC;UAAgB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtClE,OAAA;YAAK4D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC7B7D,OAAA;cAAK4D,SAAS,EAAC;YAAgB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtClE,OAAA;cAAK4D,SAAS,EAAC;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA,GALA8B,KAAK;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CACR,CAAC,GACF7C,cAAc,CAACsE,MAAM,GAAG,CAAC,GACzBtE,cAAc,CAACmE,GAAG,CAAC,CAACvC,KAAK,EAAE+C,KAAK,kBAC5BhG,OAAA;UAEI4D,SAAS,EAAE,gBAAgBX,KAAK,CAACgD,IAAI,IAAIhD,KAAK,CAACiD,QAAQ,GAAG,UAAU,GAAG,EAAE,EAAG;UAC5EjB,OAAO,EAAEA,CAAA,KAAMjC,SAAS,CAACC,KAAK,CAAE;UAChCG,KAAK,EAAE;YAAE+C,cAAc,EAAE,GAAGH,KAAK,GAAG,GAAG;UAAI,CAAE;UAAAnC,QAAA,eAE7C7D,OAAA;YAAK4D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5B7D,OAAA;cACIkB,GAAG,EAAE+B,KAAK,CAAC/B,GAAI;cACfkF,GAAG,EAAEnD,KAAK,CAAC7B,KAAM;cACjBiF,OAAO,EAAC;YAAM;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EACDjB,KAAK,CAACiD,QAAQ,iBACXlG,OAAA;cAAK4D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3B7D,OAAA;gBAAKmE,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,eACvD7D,OAAA;kBAASsG,MAAM,EAAC,2FAA2F;kBAAChC,IAAI,EAAC;gBAAc;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChI,CAAC,YAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACR,eACDlE,OAAA;cAAK4D,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC1B7D,OAAA;gBAAK4D,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC5B7D,OAAA;kBAAI4D,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEZ,KAAK,CAAC7B;gBAAK;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9ClE,OAAA;kBAAG4D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,EAAEZ,KAAK,CAACsD;gBAAW;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxDlE,OAAA;kBAAK4D,SAAS,EAAC,YAAY;kBAAAC,QAAA,EACtBZ,KAAK,CAACuD,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjB,GAAG,CAACkB,GAAG,iBAC3B1G,OAAA;oBAAgB4D,SAAS,EAAC,KAAK;oBAAAC,QAAA,GAAC,GAAC,EAAC6C,GAAG;kBAAA,GAA1BA,GAAG;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA8B,CAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNlE,OAAA;kBAAK4D,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC5B7D,OAAA;oBAAQ4D,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBAClC7D,OAAA;sBAAKmE,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAT,QAAA,gBACvD7D,OAAA;wBAAM4E,CAAC,EAAC,mDAAmD;wBAACF,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACnGlE,OAAA;wBAAQuE,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,GAAG;wBAACC,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC,gBAEV;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTlE,OAAA;oBAAQ4D,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACpC7D,OAAA;sBAAKmE,KAAK,EAAC,IAAI;sBAACC,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAT,QAAA,gBACvD7D,OAAA;wBAAM4E,CAAC,EAAC,yHAAyH;wBAACF,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eACzKlE,OAAA;wBAAM4E,CAAC,EAAC,gHAAgH;wBAACF,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAChKlE,OAAA;wBAAM4E,CAAC,EAAC,yHAAyH;wBAACF,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxK,CAAC,QAEV;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC,GA/CDjB,KAAK,CAAClC,EAAE;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgDZ,CACR,CAAC,gBAEFlE,OAAA;UAAK4D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB7D,OAAA;YAAK4D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC5B7D,OAAA;cAAKmE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAT,QAAA,gBACvD7D,OAAA;gBAAQuE,EAAE,EAAC,IAAI;gBAACC,EAAE,EAAC,IAAI;gBAACC,CAAC,EAAC,GAAG;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC;cAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eACrElE,OAAA;gBAAM4E,CAAC,EAAC,oBAAoB;gBAACF,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC;cAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNlE,OAAA;YAAA6D,QAAA,EAAI;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBlE,OAAA;YAAA6D,QAAA,EAAG;UAAmD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1DlE,OAAA;YACI4D,SAAS,EAAC,iBAAiB;YAC3BqB,OAAO,EAAEA,CAAA,KAAM;cACXvE,aAAa,CAAC,EAAE,CAAC;cACjBN,mBAAmB,CAAC,KAAK,CAAC;YAC9B,CAAE;YAAAyD,QAAA,EACL;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGVlE,OAAA;QAAS4D,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC5B7D,OAAA;UAAK4D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC3B7D,OAAA;YAAK4D,SAAS,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACNlE,OAAA;UAAK4D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB7D,OAAA;YAAK4D,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACrB7D,OAAA;cAAA6D,QAAA,EAAI;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3ClE,OAAA;cAAA6D,QAAA,EAAG;YAIH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlE,OAAA;cAAK4D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB7D,OAAA;gBAAK4D,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACpB7D,OAAA;kBAAKmE,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAT,QAAA,eACvD7D,OAAA;oBAAM4E,CAAC,EAAC,iBAAiB;oBAACF,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACgC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC,eACNlE,OAAA;kBAAA6D,QAAA,EAAM;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACNlE,OAAA;gBAAK4D,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACpB7D,OAAA;kBAAKmE,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAT,QAAA,eACvD7D,OAAA;oBAAM4E,CAAC,EAAC,iBAAiB;oBAACF,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACgC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC,eACNlE,OAAA;kBAAA6D,QAAA,EAAM;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACNlE,OAAA;gBAAK4D,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACpB7D,OAAA;kBAAKmE,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAT,QAAA,eACvD7D,OAAA;oBAAM4E,CAAC,EAAC,iBAAiB;oBAACF,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACgC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7G,CAAC,eACNlE,OAAA;kBAAA6D,QAAA,EAAM;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACNlE,OAAA;YAAK4D,SAAS,EAAC,aAAa;YAAAC,QAAA,gBACxB7D,OAAA,CAACF,IAAI;cAACgE,EAAE,EAAC,WAAW;cAACF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACtD7D,OAAA;gBAAKmE,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvD7D,OAAA;kBAAM4E,CAAC,EAAC,gLAAgL;kBAACF,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChOlE,OAAA;kBAAM4E,CAAC,EAAC,SAAS;kBAACF,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACzDlE,OAAA;kBAAM4E,CAAC,EAAC,8JAA8J;kBAACF,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7M,CAAC,mBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlE,OAAA,CAACF,IAAI;cAACgE,EAAE,EAAC,UAAU;cAACF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACrD7D,OAAA;gBAAKmE,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,eACvD7D,OAAA;kBAAM4E,CAAC,EAAC,sPAAsP;kBAACF,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrS,CAAC,oBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,EAGL7D,aAAa,iBACVL,OAAA;MAAK4D,SAAS,EAAC,eAAe;MAACqB,OAAO,EAAE3B,UAAW;MAAAO,QAAA,eAC/C7D,OAAA;QAAK4D,SAAS,EAAC,eAAe;QAACqB,OAAO,EAAGzB,CAAC,IAAKA,CAAC,CAACqD,eAAe,CAAC,CAAE;QAAAhD,QAAA,gBAC/D7D,OAAA;UAAQ4D,SAAS,EAAC,aAAa;UAACqB,OAAO,EAAE3B,UAAW;UAAAO,QAAA,eAChD7D,OAAA;YAAKmE,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAAAT,QAAA,gBACvD7D,OAAA;cAAMkF,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACX,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC;YAAG;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAC3ElE,OAAA;cAAMkF,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,GAAG;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACX,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC;YAAG;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAETlE,OAAA;UAAK4D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxB7D,OAAA;YAAKkB,GAAG,EAAEb,aAAa,CAACa,GAAI;YAACkF,GAAG,EAAE/F,aAAa,CAACe;UAAM;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACxD7D,aAAa,CAAC6F,QAAQ,iBACnBlG,OAAA;YAAK4D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjC7D,OAAA;cAAKmE,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAT,QAAA,eACvD7D,OAAA;gBAASsG,MAAM,EAAC,2FAA2F;gBAAChC,IAAI,EAAC;cAAc;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC,YAEV;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAENlE,OAAA;UAAK4D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB7D,OAAA;YAAK4D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB7D,OAAA;cAAA6D,QAAA,EAAKxD,aAAa,CAACe;YAAK;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9BlE,OAAA;cAAM4D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAExD,aAAa,CAACc;YAAQ;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNlE,OAAA;YAAG4D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAExD,aAAa,CAACkG;UAAW;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEhElE,OAAA;YAAK4D,SAAS,EAAC,YAAY;YAAAC,QAAA,EACtBxD,aAAa,CAACmG,IAAI,CAAChB,GAAG,CAACkB,GAAG,iBACvB1G,OAAA;cAAgB4D,SAAS,EAAC,WAAW;cAAAC,QAAA,GAAC,GAAC,EAAC6C,GAAG;YAAA,GAAhCA,GAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoC,CACrD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENlE,OAAA;YAAK4D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1B7D,OAAA,CAACF,IAAI;cACDgE,EAAE,EAAC,WAAW;cACdF,SAAS,EAAC,iBAAiB;cAC3BqB,OAAO,EAAE3B,UAAW;cAAAO,QAAA,gBAEpB7D,OAAA;gBAAKmE,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAT,QAAA,gBACvD7D,OAAA;kBAAM4E,CAAC,EAAC,gLAAgL;kBAACF,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAChOlE,OAAA;kBAAM4E,CAAC,EAAC,SAAS;kBAACF,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,iBAEV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlE,OAAA;cAAQ4D,SAAS,EAAC,iBAAiB;cAACqB,OAAO,EAAE3B,UAAW;cAAAO,QAAA,EAAC;YAEzD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAChE,EAAA,CAhaID,OAAO;AAAA6G,EAAA,GAAP7G,OAAO;AAkab,eAAeA,OAAO;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}