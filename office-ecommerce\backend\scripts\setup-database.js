const sql = require('mssql');
require('dotenv').config();

const masterConfig = {
    server: process.env.DB_SERVER || 'localhost',
    database: 'master', // Connect to master database first
    port: parseInt(process.env.DB_PORT) || 1433,
    options: {
        encrypt: process.env.DB_ENCRYPT === 'true' || false,
        trustServerCertificate: process.env.DB_TRUST_CERT === 'true' || true,
        enableArithAbort: true,
        requestTimeout: 30000,
        connectionTimeout: 30000,
        integratedSecurity: process.env.DB_WINDOWS_AUTH === 'true' || false
    }
};

// Add user/password only if not using Windows Authentication
if (process.env.DB_WINDOWS_AUTH !== 'true') {
    masterConfig.user = process.env.DB_USER || 'sa';
    masterConfig.password = process.env.DB_PASSWORD || 'password';
}

const setupDatabase = async () => {
    let pool;
    try {
        console.log('🔄 Connecting to SQL Server...');
        pool = await sql.connect(masterConfig);
        console.log('✅ Connected to SQL Server');

        // Check if database exists
        const dbName = process.env.DB_DATABASE || 'OfficeEcommerce';
        console.log(`🔍 Checking if database '${dbName}' exists...`);

        const result = await pool.request()
            .input('dbName', sql.NVarChar, dbName)
            .query(`
                SELECT database_id
                FROM sys.databases
                WHERE name = @dbName
            `);

        if (result.recordset.length === 0) {
            console.log(`📝 Creating database '${dbName}'...`);
            await pool.request()
                .query(`CREATE DATABASE [${dbName}]`);
            console.log(`✅ Database '${dbName}' created successfully`);
        } else {
            console.log(`✅ Database '${dbName}' already exists`);
        }

        // Test connection to the target database
        await pool.close();

        const targetConfig = {
            ...masterConfig,
            database: dbName
        };

        console.log(`🔄 Testing connection to '${dbName}' database...`);
        pool = await sql.connect(targetConfig);
        await pool.request().query('SELECT 1 as test');
        console.log(`✅ Successfully connected to '${dbName}' database`);

        await pool.close();
        console.log('🎉 Database setup completed successfully!');

    } catch (error) {
        console.error('❌ Database setup failed:', error.message);
        console.error('Full error:', error);

        if (error.message.includes('Login failed')) {
            console.log('\n💡 Troubleshooting tips:');
            console.log('1. Check if SQL Server is running');
            console.log('2. Verify the username and password in .env file');
            console.log('3. Ensure SQL Server Authentication is enabled');
            console.log('4. Check if the sa account is enabled');
        }

        if (error.message.includes('Could not connect')) {
            console.log('\n💡 Connection troubleshooting:');
            console.log('1. Check if SQL Server is listening on port 1433');
            console.log('2. Verify firewall settings');
            console.log('3. Check SQL Server Configuration Manager');
        }

        process.exit(1);
    } finally {
        if (pool) {
            await pool.close();
        }
    }
};

// Run the setup
setupDatabase();
