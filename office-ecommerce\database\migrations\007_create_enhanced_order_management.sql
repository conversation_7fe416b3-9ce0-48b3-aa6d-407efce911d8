USE OfficeEcommerce;
GO

-- Create OrderStatus table for better status management
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrderStatus' AND xtype='U')
BEGIN
    CREATE TABLE OrderStatus (
        id INT PRIMARY KEY IDENTITY(1,1),
        name NVARCHAR(50) NOT NULL UNIQUE,
        description NVARCHAR(255) NULL,
        color NVARCHAR(7) NULL, -- Hex color for UI display
        sortOrder INT DEFAULT 0,
        isActive BIT DEFAULT 1,
        isFinal BIT DEFAULT 0, -- Whether this is a final status (delivered, cancelled)
        createdAt DATETIME DEFAULT GETDATE()
    );

    -- Insert default order statuses
    INSERT INTO OrderStatus (name, description, color, sortOrder, isFinal) VALUES
    ('pending', 'Order placed, awaiting confirmation', '#FFA500', 1, 0),
    ('confirmed', 'Order confirmed, preparing for processing', '#2196F3', 2, 0),
    ('processing', 'Order being processed and prepared', '#FF9800', 3, 0),
    ('shipped', 'Order shipped and in transit', '#9C27B0', 4, 0),
    ('delivered', 'Order successfully delivered', '#4CAF50', 5, 1),
    ('cancelled', 'Order cancelled', '#F44336', 6, 1),
    ('returned', 'Order returned by customer', '#795548', 7, 1),
    ('refunded', 'Order refunded', '#607D8B', 8, 1);

    -- Create indexes
    CREATE INDEX IX_OrderStatus_Name ON OrderStatus(name);
    CREATE INDEX IX_OrderStatus_SortOrder ON OrderStatus(sortOrder);
    CREATE INDEX IX_OrderStatus_IsActive ON OrderStatus(isActive);

    PRINT 'OrderStatus table created successfully';
END
ELSE
BEGIN
    PRINT 'OrderStatus table already exists';
END
GO

-- Create OrderItems table (normalized from JSON in Orders table)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrderItems' AND xtype='U')
BEGIN
    CREATE TABLE OrderItems (
        id INT PRIMARY KEY IDENTITY(1,1),
        orderId INT NOT NULL,
        productId INT NOT NULL,
        variantId INT NULL, -- For product variants
        sku NVARCHAR(100) NOT NULL,
        productName NVARCHAR(255) NOT NULL,
        variantName NVARCHAR(255) NULL,
        quantity INT NOT NULL,
        unitPrice DECIMAL(10,2) NOT NULL,
        totalPrice AS (quantity * unitPrice) PERSISTED,
        customization NVARCHAR(MAX) NULL, -- JSON of customization options
        specifications NVARCHAR(MAX) NULL, -- JSON of product specifications at time of order
        notes NVARCHAR(MAX) NULL,
        createdAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_OrderItems_Orders FOREIGN KEY (orderId) REFERENCES Orders(id) ON DELETE CASCADE,
        CONSTRAINT FK_OrderItems_Products FOREIGN KEY (productId) REFERENCES Products(id),
        CONSTRAINT FK_OrderItems_ProductVariants FOREIGN KEY (variantId) REFERENCES ProductVariants(id),
        CONSTRAINT CK_OrderItems_Quantity CHECK (quantity > 0),
        CONSTRAINT CK_OrderItems_UnitPrice CHECK (unitPrice >= 0)
    );

    -- Create indexes
    CREATE INDEX IX_OrderItems_OrderId ON OrderItems(orderId);
    CREATE INDEX IX_OrderItems_ProductId ON OrderItems(productId);
    CREATE INDEX IX_OrderItems_VariantId ON OrderItems(variantId);
    CREATE INDEX IX_OrderItems_SKU ON OrderItems(sku);

    PRINT 'OrderItems table created successfully';
END
ELSE
BEGIN
    PRINT 'OrderItems table already exists';
END
GO

-- Create OrderStatusHistory table for tracking status changes
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrderStatusHistory' AND xtype='U')
BEGIN
    CREATE TABLE OrderStatusHistory (
        id INT PRIMARY KEY IDENTITY(1,1),
        orderId INT NOT NULL,
        fromStatusId INT NULL, -- NULL for initial status
        toStatusId INT NOT NULL,
        changedBy INT NOT NULL,
        reason NVARCHAR(255) NULL,
        notes NVARCHAR(MAX) NULL,
        changedAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_OrderStatusHistory_Orders FOREIGN KEY (orderId) REFERENCES Orders(id) ON DELETE CASCADE,
        CONSTRAINT FK_OrderStatusHistory_FromStatus FOREIGN KEY (fromStatusId) REFERENCES OrderStatus(id),
        CONSTRAINT FK_OrderStatusHistory_ToStatus FOREIGN KEY (toStatusId) REFERENCES OrderStatus(id),
        CONSTRAINT FK_OrderStatusHistory_ChangedBy FOREIGN KEY (changedBy) REFERENCES Users(id)
    );

    -- Create indexes
    CREATE INDEX IX_OrderStatusHistory_OrderId ON OrderStatusHistory(orderId);
    CREATE INDEX IX_OrderStatusHistory_ToStatusId ON OrderStatusHistory(toStatusId);
    CREATE INDEX IX_OrderStatusHistory_ChangedAt ON OrderStatusHistory(changedAt);
    CREATE INDEX IX_OrderStatusHistory_ChangedBy ON OrderStatusHistory(changedBy);

    PRINT 'OrderStatusHistory table created successfully';
END
ELSE
BEGIN
    PRINT 'OrderStatusHistory table already exists';
END
GO

-- Create DeliveryTracking table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DeliveryTracking' AND xtype='U')
BEGIN
    CREATE TABLE DeliveryTracking (
        id INT PRIMARY KEY IDENTITY(1,1),
        orderId INT NOT NULL,
        carrier NVARCHAR(100) NULL, -- 'FedEx', 'UPS', 'DHL', etc.
        trackingNumber NVARCHAR(100) NULL,
        shippingMethod NVARCHAR(100) NULL, -- 'Standard', 'Express', 'Overnight'
        estimatedDeliveryDate DATETIME NULL,
        actualDeliveryDate DATETIME NULL,
        shippingAddress NVARCHAR(500) NOT NULL,
        billingAddress NVARCHAR(500) NULL,
        shippingCost DECIMAL(10,2) NOT NULL DEFAULT 0,
        weight DECIMAL(8,2) NULL,
        dimensions NVARCHAR(MAX) NULL, -- JSON: {"length": 100, "width": 50, "height": 30}
        packageCount INT DEFAULT 1,
        deliveryInstructions NVARCHAR(MAX) NULL,
        signatureRequired BIT DEFAULT 0,
        deliveredTo NVARCHAR(255) NULL, -- Name of person who received
        deliveryNotes NVARCHAR(MAX) NULL,
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_DeliveryTracking_Orders FOREIGN KEY (orderId) REFERENCES Orders(id) ON DELETE CASCADE,
        CONSTRAINT CK_DeliveryTracking_ShippingCost CHECK (shippingCost >= 0),
        CONSTRAINT CK_DeliveryTracking_PackageCount CHECK (packageCount > 0)
    );

    -- Create indexes
    CREATE INDEX IX_DeliveryTracking_OrderId ON DeliveryTracking(orderId);
    CREATE INDEX IX_DeliveryTracking_TrackingNumber ON DeliveryTracking(trackingNumber);
    CREATE INDEX IX_DeliveryTracking_Carrier ON DeliveryTracking(carrier);
    CREATE INDEX IX_DeliveryTracking_EstimatedDeliveryDate ON DeliveryTracking(estimatedDeliveryDate);

    PRINT 'DeliveryTracking table created successfully';
END
ELSE
BEGIN
    PRINT 'DeliveryTracking table already exists';
END
GO

-- Create ProductConfigurations table for custom 3D configurations
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ProductConfigurations' AND xtype='U')
BEGIN
    CREATE TABLE ProductConfigurations (
        id INT PRIMARY KEY IDENTITY(1,1),
        userId INT NULL, -- NULL for guest configurations
        productId INT NOT NULL,
        configurationName NVARCHAR(255) NULL,
        configurationData NVARCHAR(MAX) NOT NULL, -- JSON of 3D configuration
        previewImage NVARCHAR(255) NULL, -- Path to generated preview image
        totalPrice DECIMAL(10,2) NOT NULL,
        isPublic BIT DEFAULT 0, -- Whether configuration can be shared
        isSaved BIT DEFAULT 0, -- Whether user saved this configuration
        sessionId NVARCHAR(255) NULL, -- For guest users
        createdAt DATETIME DEFAULT GETDATE(),
        updatedAt DATETIME DEFAULT GETDATE(),

        CONSTRAINT FK_ProductConfigurations_Users FOREIGN KEY (userId) REFERENCES Users(id),
        CONSTRAINT FK_ProductConfigurations_Products FOREIGN KEY (productId) REFERENCES Products(id) ON DELETE CASCADE,
        CONSTRAINT CK_ProductConfigurations_TotalPrice CHECK (totalPrice >= 0)
    );

    -- Create indexes
    CREATE INDEX IX_ProductConfigurations_UserId ON ProductConfigurations(userId);
    CREATE INDEX IX_ProductConfigurations_ProductId ON ProductConfigurations(productId);
    CREATE INDEX IX_ProductConfigurations_SessionId ON ProductConfigurations(sessionId);
    CREATE INDEX IX_ProductConfigurations_IsPublic ON ProductConfigurations(isPublic);
    CREATE INDEX IX_ProductConfigurations_IsSaved ON ProductConfigurations(isSaved);

    PRINT 'ProductConfigurations table created successfully';
END
ELSE
BEGIN
    PRINT 'ProductConfigurations table already exists';
END
GO

-- Add foreign key to link OrderItems with ProductConfigurations
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_OrderItems_ProductConfigurations')
BEGIN
    ALTER TABLE OrderItems
    ADD configurationId INT NULL,
    CONSTRAINT FK_OrderItems_ProductConfigurations FOREIGN KEY (configurationId) REFERENCES ProductConfigurations(id);

    CREATE INDEX IX_OrderItems_ConfigurationId ON OrderItems(configurationId);

    PRINT 'Added configurationId to OrderItems table';
END
ELSE
BEGIN
    PRINT 'OrderItems already has configurationId column';
END
GO
