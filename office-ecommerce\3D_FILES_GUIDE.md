# 3D Files Usage Guide for Office E-commerce Project

## Overview

This project supports both **procedural 3D models** (generated with Three.js) and **external GLB/GLTF files**. You can easily switch between both approaches or use them together.

## Current 3D File Setup

### 📁 Directory Structure
```
office-ecommerce/
├── frontend/
│   ├── public/
│   │   └── models/                 # 3D model files go here
│   │       ├── README.md          # Model requirements guide
│   │       └── S-407Chair.gltf    # Sample chair model
│   └── src/
│       └── components/
│           └── 3d/
│               ├── TableConfigurator.js      # Original procedural configurator
│               ├── Enhanced3DConfigurator.js # New GLB + procedural configurator
│               └── GLBLoader.js              # GLB file loader utility
```

## 🎯 How to Add Your GLB Files

### Step 1: Prepare Your GLB Files
1. **Export from 3D software** (Blender, 3ds Max, Maya, etc.)
2. **Optimize for web**:
   - Keep under 10MB per file
   - Use Draco compression if possible
   - Optimize textures (1024x1024 or smaller)
3. **Name files properly**: Use lowercase with hyphens
   - ✅ `executive-desk.glb`
   - ✅ `office-chair.glb`
   - ❌ `Executive_Desk.GLB`

### Step 2: Add Files to Project
```bash
# Place your GLB files in the models directory
office-ecommerce/frontend/public/models/
├── executive-desk.glb
├── office-chair.glb
├── filing-cabinet.glb
└── conference-table.glb
```

### Step 3: Update Product Data
Edit `src/services/products.js` to reference your GLB files:

```javascript
{
    id: 1,
    name: 'Executive Office Desk',
    model3D: '/models/executive-desk.glb',  // ← Update this path
    // ... other product properties
}
```

### Step 4: Test Your Models
1. **Start the development server**: `npm start`
2. **Navigate to a product page** with a GLB model
3. **Click "Configure in 3D"** to open the configurator
4. **Check browser console** for any loading errors

## 🔧 Using the Enhanced 3D Configurator

### Features
- **Dual Mode**: Switch between GLB files and procedural models
- **Real-time Loading**: Progress indicators for GLB files
- **Error Handling**: Automatic fallback to procedural models
- **Mobile Optimized**: Touch controls and performance optimization

### Integration Example
```javascript
import Enhanced3DConfigurator from '../components/3d/Enhanced3DConfigurator';

// In your component
const [show3DConfigurator, setShow3DConfigurator] = useState(false);

// Show configurator
if (show3DConfigurator) {
    return (
        <Enhanced3DConfigurator 
            product={product} 
            onBack={() => setShow3DConfigurator(false)} 
        />
    );
}
```

## 📋 Required GLB Files (Based on Product Data)

### Office Desks
- `executive-desk.glb` - Executive Mahogany Desk
- `glass-desk.glb` - Modern Glass Executive Desk
- `standing-desk.glb` - Standing Desk Converter
- `l-shaped-desk.glb` - L-Shaped Computer Desk
- `conference-table.glb` - Conference Table

### Office Chairs
- `executive-chair.glb` - Ergonomic Executive Chair
- `mesh-chair.glb` - Mesh Task Chair
- `S-407Chair.gltf` - Modern Mesh Task Chair (sample included)

### Storage Solutions
- `filing-cabinet.glb` - Executive Filing Cabinet
- `bookshelf.glb` - Modern Bookshelf Unit

### Accessories
- `desk-organizer.glb` - Desk Organizer Set

## 🛠️ GLB Creation Tools

### Free Options
- **Blender** - Professional 3D software with GLB export
- **SketchUp Free** - Simple 3D modeling with plugins
- **Tinkercad** - Browser-based 3D design

### Online Resources
- **Sketchfab** - Download free 3D models in GLB format
- **Poly Haven** - Free 3D assets
- **Clara.io** - Online 3D editor

### Conversion Tools
- **Online GLB Converters**:
  - https://products.aspose.app/3d/conversion/obj-to-glb
  - https://imagetostl.com/convert/file/glb
- **Command Line**: Use Blender's Python API for batch conversion

## 🔍 Testing Your GLB Files

### Online Viewers
Test your GLB files before adding them to the project:
- **Three.js Editor**: https://threejs.org/editor/
- **GLB Viewer**: https://gltf-viewer.donmccurdy.com/
- **Babylon.js Sandbox**: https://sandbox.babylonjs.com/

### Browser Testing
1. Open browser developer tools (F12)
2. Check the **Console** tab for errors
3. Check the **Network** tab to see if files are loading
4. Look for 404 errors or CORS issues

## ⚡ Performance Optimization

### File Size Optimization
```bash
# Use gltf-pipeline for optimization
npm install -g gltf-pipeline
gltf-pipeline -i model.glb -o model-optimized.glb --draco
```

### Loading Optimization
- **Preload critical models** in the main app
- **Use loading indicators** for better UX
- **Implement LOD** (Level of Detail) for complex scenes
- **Cache models** in browser storage

## 🐛 Troubleshooting

### Common Issues

**GLB file not loading:**
- Check file path is correct
- Ensure file is in `/public/models/` directory
- Verify file is not corrupted
- Check browser console for errors

**Model appears too large/small:**
- Adjust scale in GLBLoader component
- Check model units in 3D software
- Use real-world scale (1 unit = 1 meter)

**Textures not showing:**
- Ensure textures are embedded in GLB
- Check material settings
- Verify texture paths in GLTF

**Performance issues:**
- Reduce polygon count
- Optimize textures
- Use Draco compression
- Disable shadows on mobile

### Debug Mode
Enable debug logging in the GLBLoader:
```javascript
// In GLBLoader.js
console.log('Loading model:', modelPath);
console.log('Model loaded:', gltf);
```

## 🚀 Next Steps

1. **Add your GLB files** to `/public/models/`
2. **Update product data** with correct file paths
3. **Test in browser** and check for errors
4. **Optimize performance** if needed
5. **Consider implementing** model caching for production

## 📞 Support

For questions about 3D file integration:
- Check browser console for error messages
- Verify file paths and formats
- Test models in online viewers first
- Ensure files are properly optimized for web

---

**Built with Three.js and React.js** 🎨
